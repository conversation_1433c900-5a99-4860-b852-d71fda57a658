<view class="technician-time-manage"><view class="fill-base pd-lg flex-between"><view class="flex-1 flex-y-baseline f-title c-title">是否接单<view class="f-paragraph ml-sm" style="{{'color:'+(form.is_work==1?primaryColor:'#999')+';'}}">{{''+(form.is_work==1?'接单':'休息')+''}}</view></view><view data-event-opts="{{[['tap',[['toChangeItem',['$event']]]]]}}" catchtap="__e"><view class="{{['iconfont','icon-switch','c-caption','ml-sm','_i',[(form.is_work==1)?'icon-switch-on':'']]}}" style="{{'color:'+(form.is_work==1?primaryColor:'')+';'}}"></view></view></view><view class="fill-base mt-md b-1px-b"><view class="f-title c-title pd-lg">选择接单时间</view></view><view class="flex-center fill-base f-paragraph c-desc pt-lg pb-lg"><view data-event-opts="{{[['tap',[['toShowTime',['start_time']]]]]}}" class="item-time flex-center flex-column" catchtap="__e"><view>开始时间</view><view class="mt-sm" style="{{'color:'+(form.start_time?primaryColor:'#999')+';'}}">{{''+(form.start_time||'选择时间')+''}}</view></view><view data-event-opts="{{[['tap',[['toShowTime',['end_time']]]]]}}" class="item-time flex-center flex-column b-1px-l" catchtap="__e"><view>结束时间</view><view class="mt-sm" style="{{'color:'+(form.end_time?primaryColor:'#999')+';'}}">{{''+(is_next_day?'次日':'')+(form.end_time||'选择时间')+''}}</view></view></view><view class="space-max-footer"></view><w-picker class="vue-ref" vue-id="1d52dae9-1" visible="{{showTime}}" mode="time" value="{{toDayTime}}" current="{{false}}" second="{{false}}" themeColor="{{primaryColor}}" data-ref="time" data-event-opts="{{[['^updateVisible',[['__set_sync',['$0','showTime','$event'],['']]]],['^confirm',[['onConfirm']]]]}}" bind:updateVisible="__e" bind:confirm="__e" bind:__l="__l"></w-picker><block wx:if="{{form.coach_status!=3}}"><auth vue-id="1d52dae9-2" needAuth="{{userInfo&&!userInfo.nickName}}" must="{{true}}" type="userInfo" data-event-opts="{{[['^go',[['submit']]]]}}" bind:go="__e" bind:__l="__l" vue-slots="{{['default']}}"><fix-bottom-button vue-id="{{('1d52dae9-3')+','+('1d52dae9-2')}}" text="{{[{text:'保存',type:'confirm'}]}}" bgColor="#fff" bind:__l="__l"></fix-bottom-button></auth></block></view>
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-68265041 {
  background-color: #F8F8F8;
  height: 100vh;
  overflow: auto;
}
.page .box.data-v-68265041 {
  padding: 40rpx 30rpx;
}
.page .box .title.data-v-68265041 {
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .box .title2.data-v-68265041 {
  margin-top: 32rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #171717;
}
.page .box .btn.data-v-68265041 {
  margin: 0 auto;
  margin-top: 42rpx;
  width: 688rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  line-height: 98rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}
.page .main.data-v-68265041 {
  padding: 40rpx 30rpx;
}
.page .main .main_item_already.data-v-68265041 {
  padding: 28rpx 36rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
}
.page .main .main_item_already .title.data-v-68265041 {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
}
.page .main .main_item_already .ok.data-v-68265041 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #E72427;
}
.page .main .main_item_already .no.data-v-68265041 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .mid.data-v-68265041 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item_already .mid .lef.data-v-68265041 {
  display: flex;
  align-items: center;
}
.page .main .main_item_already .mid .lef image.data-v-68265041 {
  width: 120rpx;
  height: 120rpx;
}
.page .main .main_item_already .mid .lef text.data-v-68265041 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .mid .righ.data-v-68265041 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
}
.page .main .main_item_already .bot.data-v-68265041 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item_already .shifu.data-v-68265041 {
  margin-top: 20rpx;
}
.page .main .main_item_already .shifu scroll-view.data-v-68265041 {
  width: 100%;
  white-space: nowrap;
}
.page .main .main_item_already .shifu scroll-view .shifu_item.data-v-68265041 {
  display: inline-block;
  margin-right: 28rpx;
}
.page .main .main_item_already .shifu scroll-view .shifu_item .top.data-v-68265041 {
  display: flex;
}
.page .main .main_item_already .shifu scroll-view .shifu_item .top image.data-v-68265041 {
  width: 92rpx;
  height: 92rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.page .main .main_item_already .shifu scroll-view .shifu_item .top .info .name.data-v-68265041 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .main .main_item_already .shifu scroll-view .shifu_item .top .info .tag.data-v-68265041 {
  margin-top: 12rpx;
}
.page .main .main_item_already .shifu scroll-view .shifu_item .top .info .tag ._span.data-v-68265041 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 28rpx;
  background: #2E80FE;
  border-radius: 14rpx 14rpx 14rpx 14rpx;
  margin-right: 12rpx;
  line-height: 28rpx;
  text-align: center;
  font-size: 16rpx;
  font-weight: 400;
  color: #FFFFFF;
  padding: 4rpx 14rpx;
}
.page .main .main_item_already .shifu scroll-view .shifu_item text.data-v-68265041 {
  font-size: 22rpx;
  font-weight: 500;
  color: #E72427;
  text-align: center;
}
.page .main .main_item_already .btnbox.data-v-68265041 {
  display: flex;
  justify-content: space-between;
  margin-top: 52rpx;
}
.page .main .main_item_already .btnbox .btn.data-v-68265041 {
  width: 294rpx;
  height: 82rpx;
  line-height: 82rpx;
  text-align: center;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .main .main_item_already .btnbox .can.data-v-68265041 {
  border: 2rpx solid #2E80FE;
}
.page .main .main_item_already .btnbox .re.data-v-68265041 {
  background: #2E80FE;
  color: #FFFFFF;
}
.page .main .main_item_already .tips.data-v-68265041 {
  width: 100%;
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  margin-top: 20rpx;
  text-align: right;
}

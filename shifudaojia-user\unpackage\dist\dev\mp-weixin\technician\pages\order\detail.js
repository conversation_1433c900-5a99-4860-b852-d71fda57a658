(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["technician/pages/order/detail"],{

/***/ 936:
/*!***************************************************************************************!*\
  !*** E:/前端代码/shifudaojia-user/main.js?{"page":"technician%2Fpages%2Forder%2Fdetail"} ***!
  \***************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/uni-stat/dist/uni-stat.es.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _detail = _interopRequireDefault(__webpack_require__(/*! ./technician/pages/order/detail.vue */ 937));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_detail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 937:
/*!******************************************************************!*\
  !*** E:/前端代码/shifudaojia-user/technician/pages/order/detail.vue ***!
  \******************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./detail.vue?vue&type=template&id=19c3ae8a& */ 938);
/* harmony import */ var _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./detail.vue?vue&type=script&lang=js& */ 940);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 66);

var renderjs




/* normalize component */

var component = Object(_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__["render"],
  _detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "technician/pages/order/detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 938:
/*!*************************************************************************************************!*\
  !*** E:/前端代码/shifudaojia-user/technician/pages/order/detail.vue?vue&type=template&id=19c3ae8a& ***!
  \*************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=19c3ae8a& */ 939);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_19c3ae8a___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 939:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/前端代码/shifudaojia-user/technician/pages/order/detail.vue?vue&type=template&id=19c3ae8a& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.detail.id
    ? _vm.__map(_vm.detail.order_goods, function (aitem, aindex) {
        var $orig = _vm.__get_orig(aitem)
        var g0 = _vm.detail.order_goods.length
        return {
          $orig: $orig,
          g0: g0,
        }
      })
    : null
  var g1 = _vm.detail.id
    ? _vm.statusPayType.includes(_vm.detail.pay_type)
    : null
  var g2 = _vm.detail.id ? _vm.coach_refund_text.length : null
  var g3 = _vm.detail.id && !(g2 > 200) ? _vm.coach_refund_text.length : null
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      $event.stopPropagation()
      return _vm.$refs.refuse_item.close()
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        g1: g1,
        g2: g2,
        g3: g3,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 940:
/*!*******************************************************************************************!*\
  !*** E:/前端代码/shifudaojia-user/technician/pages/order/detail.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js& */ 941);
/* harmony import */ var _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_D_Backup_Documents_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 941:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/前端代码/shifudaojia-user/technician/pages/order/detail.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 36));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ 5));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 38));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 48);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var timeline = function timeline() {
  __webpack_require__.e(/*! require.ensure | components/timeline */ "components/timeline").then((function () {
    return resolve(__webpack_require__(/*! @/components/timeline.vue */ 1312));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    timeline: timeline
  },
  data: function data() {
    return {
      options: {},
      statusPayType: [2, 3, 4, 5, 6],
      statusType: {
        '-1': '已拒单',
        2: '待接单',
        3: '已接单',
        4: '已出发',
        5: '已到达',
        6: '服务中',
        7: '已完成'
      },
      carType: {
        0: '公交/地铁',
        1: '出租车'
      },
      payType: {
        0: {
          icon: 'iconweixinzhifu1 c-success',
          text: '微信支付'
        },
        1: {
          icon: 'iconqianbao',
          text: '账户余额'
        }
      },
      lineList: [{
        pay_type: 3,
        title: '技师接单',
        time: 'receiving_time',
        icon: 'iconjishijiedan'
      }, {
        pay_type: 4,
        title: '技师出发',
        time: 'serout_time',
        icon: 'iconjishichufa'
      }, {
        pay_type: 5,
        title: '技师到达',
        time: 'arrive_time',
        icon: 'iconjishidaoda'
      }, {
        pay_type: 6,
        title: '开始服务',
        time: 'start_service_time',
        icon: 'iconjishifuwu'
      }, {
        pay_type: 7,
        title: '服务完成',
        time: 'order_end_time',
        icon: 'iconjishiwancheng'
      }],
      detail: {
        pay_type: 0
      },
      popupInfo: {},
      coach_refund_text: '',
      lockTap: false
    };
  },
  computed: (0, _vuex.mapState)({
    primaryColor: function primaryColor(state) {
      return state.config.configInfo.primaryColor;
    },
    subColor: function subColor(state) {
      return state.config.configInfo.subColor;
    },
    configInfo: function configInfo(state) {
      return state.config.configInfo;
    },
    userInfo: function userInfo(state) {
      return state.user.userInfo;
    },
    over_time_text: function over_time_text() {
      return new Date().getTime() + this.detail.end_time * 1000;
    }
  }),
  onLoad: function onLoad(options) {
    this.options = options;
    this.initIndex();
  },
  methods: _objectSpread(_objectSpread(_objectSpread({}, (0, _vuex.mapActions)(['getConfigInfo'])), (0, _vuex.mapMutations)([''])), {}, {
    initIndex: function initIndex() {
      var _arguments = arguments,
        _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var refresh, id, data, pay_type, time_long, start_service_time;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                refresh = _arguments.length > 0 && _arguments[0] !== undefined ? _arguments[0] : false;
                if (!(!_this.configInfo.id || refresh)) {
                  _context.next = 4;
                  break;
                }
                _context.next = 4;
                return _this.getConfigInfo();
              case 4:
                id = _this.options.id;
                _context.next = 7;
                return _this.$api.order.orderInfo({
                  id: id
                });
              case 7:
                data = _context.sent;
                _this.$util.setNavigationBarColor({
                  bg: _this.primaryColor
                });
                data.is_balance = data.balance * 1 > 0 ? 1 : 0;
                pay_type = data.pay_type, time_long = data.time_long, start_service_time = data.start_service_time;
                if (pay_type == 6) {
                  data.start_service_time_unix = _this.$util.DateToUnix(start_service_time) + time_long * 60;
                }
                _this.detail = data;
              case 13:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }))();
    },
    initRefresh: function initRefresh() {
      this.initIndex(true);
    },
    countEnd: function countEnd() {
      var _this2 = this;
      this.$util.log("倒计时完了");
      setTimeout(function () {
        _this2.initRefresh();
        _this2.$util.back();
      }, 1000);
    },
    // 拒绝接单
    toRefuse: function toRefuse() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _this3.coach_refund_text = '';
                _this3.$refs.refuse_item.open();
              case 2:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    // 确认：拒绝接单
    confirmRefuse: function confirmRefuse() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var coach_refund_text;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                coach_refund_text = _this4.coach_refund_text;
                coach_refund_text = coach_refund_text.length > 0 ? coach_refund_text.replace(/(^\s*)|(\s*$)/g, "") : '';
                if (!(coach_refund_text.length == 0)) {
                  _context3.next = 5;
                  break;
                }
                _this4.$util.showToast({
                  title: "\u8BF7\u8F93\u5165\u62D2\u5355\u539F\u56E0"
                });
                return _context3.abrupt("return");
              case 5:
                _this4.coach_refund_text = _this4.coach_refund_text.substring(0, 200);
                _this4.toConfirm(-1);
              case 7:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    // type: -1拒绝接单，3确定接单，4已出发，5已到达，6开始服务，7完成服务
    toConfirm: function toConfirm(type) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var order_id, param, _yield$uni$chooseImag, _yield$uni$chooseImag2, res_upload, _yield$uni$chooseImag3, _yield$uni$chooseImag4, tempFiles, _yield$uni$chooseImag5, tempFilePath, _yield$_this5$$api$ba, attachment_path, _yield$_this5$$util$g, _yield$_this5$$util$g2, lat, _yield$_this5$$util$g3, lng, _yield$_this5$$util$g4, address;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                order_id = _this5.detail.id;
                param = {
                  order_id: order_id,
                  type: type
                };
                if (type == -1) {
                  param.coach_refund_text = _this5.coach_refund_text;
                }
                if (!(type == 5 || type == 7)) {
                  _context4.next = 35;
                  break;
                }
                _context4.next = 6;
                return uni.chooseImage({
                  count: 1,
                  sourceType: ['camera']
                });
              case 6:
                _yield$uni$chooseImag = _context4.sent;
                _yield$uni$chooseImag2 = (0, _slicedToArray2.default)(_yield$uni$chooseImag, 2);
                res_upload = _yield$uni$chooseImag2[0];
                _yield$uni$chooseImag3 = _yield$uni$chooseImag2[1];
                _yield$uni$chooseImag4 = _yield$uni$chooseImag3.tempFiles;
                tempFiles = _yield$uni$chooseImag4 === void 0 ? [] : _yield$uni$chooseImag4;
                _yield$uni$chooseImag5 = _yield$uni$chooseImag3.tempFilePath;
                tempFilePath = _yield$uni$chooseImag5 === void 0 ? '' : _yield$uni$chooseImag5;
                _context4.next = 16;
                return _this5.$api.base.uploadFile({
                  filePath: tempFiles[0].path,
                  filetype: 'picture'
                });
              case 16:
                _yield$_this5$$api$ba = _context4.sent;
                attachment_path = _yield$_this5$$api$ba.attachment_path;
                if (attachment_path) {
                  _context4.next = 20;
                  break;
                }
                return _context4.abrupt("return");
              case 20:
                _context4.next = 22;
                return _this5.$util.getBmapLocation();
              case 22:
                _yield$_this5$$util$g = _context4.sent;
                _yield$_this5$$util$g2 = _yield$_this5$$util$g.lat;
                lat = _yield$_this5$$util$g2 === void 0 ? '' : _yield$_this5$$util$g2;
                _yield$_this5$$util$g3 = _yield$_this5$$util$g.lng;
                lng = _yield$_this5$$util$g3 === void 0 ? '' : _yield$_this5$$util$g3;
                _yield$_this5$$util$g4 = _yield$_this5$$util$g.address;
                address = _yield$_this5$$util$g4 === void 0 ? '' : _yield$_this5$$util$g4;
                if (lat) {
                  _context4.next = 32;
                  break;
                }
                _this5.$util.showToast({
                  title: "\u8BF7\u6388\u6743\u5B9A\u4F4D\u5F53\u524D\u5730\u5740"
                });
                return _context4.abrupt("return");
              case 32:
                _this5.toConfirmUpdate(type, param, {
                  lat: lat,
                  lng: lng,
                  address: address,
                  attachment_path: attachment_path
                });
                _context4.next = 36;
                break;
              case 35:
                _this5.toConfirmUpdate(type, param);
              case 36:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    toConfirmUpdate: function toConfirmUpdate(type, param) {
      var _arguments2 = arguments,
        _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var location, lat, lng, address, attachment_path, msg;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                location = _arguments2.length > 2 && _arguments2[2] !== undefined ? _arguments2[2] : {};
                if (type == 5 || type == 7) {
                  lat = location.lat, lng = location.lng, address = location.address, attachment_path = location.attachment_path;
                  if (type == 5) {
                    param.arrive_img = attachment_path;
                    param.arr_lat = lat;
                    param.arr_lng = lng;
                    param.arr_address = address;
                  } else {
                    param.end_img = attachment_path;
                    param.end_lat = lat;
                    param.end_lng = lng;
                    param.end_address = address;
                  }
                }
                msg = {
                  '-1': '已拒绝接单',
                  3: '接单成功',
                  4: '已成功出发',
                  5: '已成功到达',
                  6: '已开始服务',
                  7: '服务已完成'
                };
                if (!_this6.lockTap) {
                  _context5.next = 5;
                  break;
                }
                return _context5.abrupt("return");
              case 5:
                _this6.lockTap = true;
                _this6.$util.showLoading();
                _context5.prev = 7;
                _context5.next = 10;
                return _this6.$api.technician.updateOrder(param);
              case 10:
                if (type == -1) {
                  _this6.$refs.refuse_item.close();
                  // this.$util.back()
                }

                _this6.$util.hideAll();
                _this6.$util.showToast({
                  title: msg[type]
                });
                _this6.lockTap = false;
                _this6.initRefresh();
                _this6.$util.back();
                _context5.next = 22;
                break;
              case 18:
                _context5.prev = 18;
                _context5.t0 = _context5["catch"](7);
                setTimeout(function () {
                  _this6.lockTap = false;
                  _this6.$util.hideAll();
                }, 2000);
                return _context5.abrupt("return");
              case 22:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[7, 18]]);
      }))();
    },
    // 咨询
    toTel: function toTel() {
      var url = this.detail.address_info.mobile;
      this.$util.goUrl({
        url: url,
        openType: "call"
      });
    },
    // 查看定位
    toMap: function toMap(key) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var _this7$detail$address, address, address_info, lat, lng;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _this7$detail$address = _this7.detail.address_info, address = _this7$detail$address.address, address_info = _this7$detail$address.address_info, lat = _this7$detail$address.lat, lng = _this7$detail$address.lng;
                _context6.next = 3;
                return _this7.$util.checkAuth({
                  type: 'userLocation'
                });
              case 3:
                _context6.next = 5;
                return uni.getLocation({
                  type: 'gcj02'
                });
              case 5:
                _context6.next = 7;
                return uni.openLocation({
                  latitude: lat * 1,
                  longitude: lng * 1,
                  name: "".concat(address, " ").concat(address_info),
                  scale: 28
                });
              case 7:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6);
      }))();
    }
  })
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ })

},[[936,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/technician/pages/order/detail.js.map
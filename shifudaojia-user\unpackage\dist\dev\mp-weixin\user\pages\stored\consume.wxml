<view class="stored-record-pages"><fixed vue-id="2169c345-1" bind:__l="__l" vue-slots="{{['default']}}"><view class="list-time flex-center f-paragraph c-title fill-base b-1px-b"><block wx:for="{{rankList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toChangeItem',[index,'rankInd']]]]]}}" class="item-child flex-center" style="{{'color:'+(rankInd==index?primaryColor:'')+';'}}" catchtap="__e">{{item+''}}<block wx:if="{{item=='日期筛选'}}"><view class="iconfont icon-right rotate-90 ml-sm _i"></view></block></view></block></view></fixed><block wx:for="{{list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toChangeItem',[index,'curInd']]]]]}}" class="cate-item-area mt-md ml-lg mr-lg fill-base radius-16" catchtap="__e"><view class="content f-title c-title pd-lg"><view class="flex-between"><view class="max-446 ellipsis">{{item.goods_title}}</view><view class="c-warning">{{(item.add==1?'+':'-')+item.price}}</view></view><view class="flex-between f-caption c-caption"><view>{{item.create_time}}</view><view>{{"余额："+item.after_balance}}</view></view></view></view></block><block wx:if="{{loading}}"><load-more vue-id="2169c345-2" noMore="{{$root.g0}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g1}}"><abnor vue-id="2169c345-3" bind:__l="__l"></abnor></block><view class="space-footer"></view><uni-popup class="vue-ref" vue-id="2169c345-4" type="top" custom="{{true}}" maskClick="{{false}}" data-ref="show_choose_time" bind:__l="__l" vue-slots="{{['default']}}"><view style="height:95rpx;"></view><view class="popup-choose-time fill-base f-paragraph c-desc pt-lg pb-lg"><view class="flex-center"><view data-event-opts="{{[['tap',[['toShowTime',['start_time']]]]]}}" class="item-child flex-center flex-column" catchtap="__e"><view>开始时间</view><view class="mt-sm" style="{{'color:'+(param.start_time?primaryColor:'#999')+';'}}">{{''+(param.start_time||'选择时间')+''}}</view></view><view data-event-opts="{{[['tap',[['toShowTime',['end_time']]]]]}}" class="item-child flex-center flex-column b-1px-l" catchtap="__e"><view>结束时间</view><view class="mt-sm" style="{{'color:'+(param.end_time?primaryColor:'#999')+';'}}">{{''+(param.end_time||'选择时间')+''}}</view></view></view></view></uni-popup><w-picker class="vue-ref" vue-id="2169c345-5" mode="date" startYear="{{startYear}}" endYear="{{startYear*1+100}}" value="{{toDay}}" current="{{false}}" fields="day" disabled-after="{{false}}" themeColor="{{primaryColor}}" visible="{{showDate}}" data-ref="day" data-event-opts="{{[['^confirm',[['onConfirm',['$event']]]],['^updateVisible',[['__set_sync',['$0','showDate','$event'],['']]]]]}}" bind:confirm="__e" bind:updateVisible="__e" bind:__l="__l"></w-picker></view>
<view style="background-color:#F4F6F7;padding:20rpx 0;"><view class="hideCanvasView"><l-painter class="hideCanvas vue-ref" vue-id="62c0de68-1" data-ref="painter" bind:__l="__l"></l-painter></view><block wx:if="{{src}}"><block><image class="code-img" src="{{src}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="btns"><button data-event-opts="{{[['tap',[['saveImage',['$event']]]]]}}" class="save-btn flex-center radius" style="{{'background:'+(primaryColor)+';'}}" bindtap="__e">保存图片至相册</button></view></block></block></view>
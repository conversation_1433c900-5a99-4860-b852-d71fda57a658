<view class="page data-v-56e85722"><view class="main data-v-56e85722"><u-picker vue-id="f3b9e3b8-1" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeHandler']]],['^cancel',[['e0']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-56e85722 vue-ref" bind:__l="__l"></u-picker><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>姓名</view><input type="text" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','coach_name','$event',[]],['form']]]]]}}" value="{{form.coach_name}}" bindinput="__e" class="data-v-56e85722"/></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>手机号</view><input type="text" placeholder="请输入手机号" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e" class="data-v-56e85722"/></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>性别</view><u-radio-group bind:input="__e" vue-id="f3b9e3b8-2" placement="row" value="{{form.sex}}" data-event-opts="{{[['^input',[['__set_model',['$0','sex','$event',[]],['form']]]]]}}" class="data-v-56e85722" bind:__l="__l" vue-slots="{{['default']}}"><u-radio vue-id="{{('f3b9e3b8-3')+','+('f3b9e3b8-2')}}" customStyle="{{({marginRight:'20px'})}}" label="男" name="{{0}}" class="data-v-56e85722" bind:__l="__l"></u-radio><u-radio vue-id="{{('f3b9e3b8-4')+','+('f3b9e3b8-2')}}" label="女" name="{{1}}" class="data-v-56e85722" bind:__l="__l"></u-radio></u-radio-group></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>从业年份</view><input type="text" placeholder="请输入从业年份" data-event-opts="{{[['input',[['__set_model',['$0','work_time','$event',[]],['form']]]]]}}" value="{{form.work_time}}" bindinput="__e" class="data-v-56e85722"/></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>所在地址</view><input type="text" placeholder="请输入所在地址" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['form']]]]]}}" value="{{form.address}}" bindinput="__e" class="data-v-56e85722"/></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>选择区域</view><input type="text" placeholder="请选择代理区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e1',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e" class="data-v-56e85722"/></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>自我介绍</view><input type="text" placeholder="请输入自我介绍" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['form']]]]]}}" value="{{form.text}}" bindinput="__e" class="data-v-56e85722"/></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>身份证号</view><input type="text" placeholder="请输入身份证号" data-event-opts="{{[['input',[['__set_model',['$0','id_code','$event',[]],['form']]]]]}}" value="{{form.id_code}}" bindinput="__e" class="data-v-56e85722"/></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>上传身份证照片</view><view class="card data-v-56e85722"><view class="card_item data-v-56e85722"><view class="top data-v-56e85722"><view class="das data-v-56e85722"><view class="up data-v-56e85722"><upload vue-id="f3b9e3b8-5" imagelist="{{form.id_card1}}" imgtype="id_card1" imgclass="id_card_box" text="身份证人像面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" class="data-v-56e85722" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-56e85722">拍摄人像面</view></view><view class="card_item data-v-56e85722"><view class="top data-v-56e85722"><view class="das data-v-56e85722"><view class="up data-v-56e85722"><upload vue-id="f3b9e3b8-6" imagelist="{{form.id_card2}}" imgtype="id_card2" imgclass="id_card_box" text="身份证国徽面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" class="data-v-56e85722" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-56e85722">拍摄国徽面</view></view></view></view><view class="main_item data-v-56e85722"><view class="title data-v-56e85722"><label class="_span data-v-56e85722">*</label>上传形象照片</view><upload vue-id="f3b9e3b8-7" imagelist="{{form.self_img}}" imgtype="self_img" imgclass text="形象照片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-56e85722" bind:__l="__l"></upload></view></view><view class="footer data-v-56e85722"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="btn data-v-56e85722" bindtap="__e">保存</view></view></view>
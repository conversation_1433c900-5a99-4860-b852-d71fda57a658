<block wx:if="{{detail.id}}"><view class="pages-home"><banner vue-id="60511c24-1" list="{{detail.imgs}}" margin="{{0}}" autoplay="{{true}}" height="{{562}}" indicatorActiveColor="{{primaryColor}}" bind:__l="__l"></banner><view class="fill-base pd-lg"><view class="f-title c-title">{{detail.title}}</view><view class="flex-y-baseline f-desc c-caption mt-sm"><view class="flex-y-baseline flex-1"><block wx:if="{{detail.init_price}}"><view class="text-delete mr-sm">{{"¥"+detail.init_price}}</view></block><view class="f-md-title c-warning mr-sm">{{"¥"+detail.price}}</view>{{'/ '+detail.time_long+'分钟'}}</view><view>{{detail.total_sale+"人选择"}}</view></view></view><view class="space-md"></view><tab vue-id="60511c24-2" list="{{tabList}}" activeIndex="{{activeIndex*1}}" activeColor="{{primaryColor}}" width="33.3%" height="100rpx" data-event-opts="{{[['^change',[['handerTabChange']]]]}}" bind:change="__e" bind:__l="__l"></tab><view class="space-md"></view><view class="fill-base pd-lg f-paragraph c-desc"><view class="c-title text-bold mb-md">{{tabList[activeIndex].title}}</view><parser vue-id="60511c24-3" html="{{detail[rule[activeIndex]]}}" show-with-animation="{{true}}" lazy-load="{{true}}" data-event-opts="{{[['^linkpress',[['linkpress']]]]}}" bind:linkpress="__e" bind:__l="__l" vue-slots="{{['default']}}">加载中...</parser></view><view class="space-max-footer"></view><fix-bottom-button vue-id="60511c24-4" text="{{[{type:'confirm',text:'选择技师'}]}}" bgColor="#fff" data-event-opts="{{[['^confirm',[['e0']]]]}}" bind:confirm="__e" bind:__l="__l"></fix-bottom-button></view></block>
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99999;
  overflow: hidden;
}
.uni-popup__wrapper {
  position: absolute;
  z-index: 999;
  box-sizing: border-box;
}
.uni-popup__wrapper.ani {
  transition: all 0.3s;
}
.uni-popup__wrapper.top {
  top: 0;
  width: 705rpx;
  left: 0;
  -webkit-transform: translateY(-705rpx);
          transform: translateY(-705rpx);
}
.uni-popup__wrapper-box {
  position: relative;
  box-sizing: border-box;
}
.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box {
  width: 705rpx;
  margin: 0 22.5rpx;
  padding: 30rpx;
  background: #fff;
  border: solid 2rpx #ddd;
  box-sizing: border-box;
  border-radius: 16rpx;
}
.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box .title {
  font-size: 32rpx;
  font-weight: bold;
}
.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box .content {
  margin-top: 16rpx;
  line-height: 1.6;
}
.uni-popup__wrapper.uni-custom.top .uni-popup__wrapper-box {
  width: 705rpx;
}
.uni-popup__wrapper.uni-top {
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.f_r_sb_c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pages-mine {
  background-color: #fff;
  padding-bottom: 120rpx;
  overflow: auto;
}
.pages-mine .box1 {
  border-radius: 36rpx 36rpx 0rpx 0rpx;
}
.pages-mine .mine-bg {
  width: 100%;
  height: 368rpx;
  z-index: 0;
}
.pages-mine .mine-master-bg {
  width: 100%;
  height: 514rpx;
  z-index: -1;
}
.pages-mine .avatar_view {
  width: 120rpx;
  height: 120rpx;
  position: relative;
}
.pages-mine .avatar_view .avatar {
  width: 120rpx;
  height: 120rpx;
  overflow: hidden;
}
.pages-mine .avatar_view .avatar open-data {
  width: 120rpx;
  height: 120rpx;
}
.pages-mine .avatar_view .text {
  width: 110rpx;
  position: absolute;
  bottom: -5rpx;
  left: 5rpx;
  height: 36rpx;
  line-height: 36rpx;
  background: #FFFFFF;
  border-radius: 18rpx;
  color: #A40035;
  font-size: 24rpx;
  text-align: center;
}
.pages-mine .member-tag {
  min-width: 168rpx;
  height: 42rpx;
  background: rgba(255, 255, 255, 0.5);
}
.pages-mine .member-tag .iconfont {
  font-size: 28rpx;
}
.pages-mine .icon-shuaxin,
.pages-mine .icon-xitong {
  font-size: 40rpx;
}
.pages-mine .share-img {
  width: 86rpx;
  height: 86rpx;
}
.pages-mine .mine-count-list .cancel-auth {
  width: 110rpx;
  height: 100rpx;
  font-size: 100rpx;
  top: 100rpx;
  left: 150rpx;
}
.pages-mine .mine-count-list .cancel-auth .text-bold {
  height: 26rpx;
  -webkit-transform: rotate(-32deg);
          transform: rotate(-32deg);
}
.pages-mine .mine-count-list .cash-btn {
  width: 138rpx;
  height: 52rpx;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
}
.pages-mine .mine-count-list .icon-right {
  font-size: 28rpx;
}
.pages-mine .share-list .coupon-img {
  width: 97rpx;
  height: 87rpx;
}
.pages-mine .share-list .item-icon {
  width: 70rpx;
  height: 70rpx;
}
.pages-mine .share-list .item-icon .iconfont {
  font-size: 38rpx;
}
.pages-mine .share-list .item-icon .item-icon {
  top: 0;
  left: 0;
  opacity: 0.1;
}
.pages-mine .mine-menu-list .menu-title {
  height: 90rpx;
}
.pages-mine .mine-menu-list .menu-title .iconfont {
  font-size: 24rpx;
}
.pages-mine .mine-menu-list .item-child {
  width: 25%;
  margin: 10rpx 0;
}
.pages-mine .mine-menu-list .item-child .iconfont {
  font-size: 52rpx;
}
.pages-mine .mine-menu-list .item-child .item-img {
  width: 88rpx;
  height: 88rpx;
}
.pages-mine .mine-menu-list .item-child .item-img .iconfont {
  font-size: 44rpx;
}
.pages-mine .mine-menu-list .item-child .item-img .item-img {
  top: 0;
  left: 0;
  opacity: 0.1;
}
.pages-mine .mine-tool-list {
  box-shadow: 0px 3px 6px 0px rgba(227, 227, 227, 0.47);
}
.pages-mine .mine-tool-list .list-item .iconfont {
  font-size: 42rpx;
}
.pages-mine .mine-tool-list .list-item .icon-right {
  font-size: 28rpx;
}
.pages-mine .mine-tool-list .list-item .icon-switch {
  font-size: 70rpx;
  line-height: 48rpx;
}
.pages-mine .mine-tool-list .list-item.b-1px-t:before {
  left: 60rpx;
}
.pages-mine .help-img-info {
  width: 130rpx;
  height: 130rpx;
  right: 100rpx;
}
.pages-mine .help-img-info .bg-img {
  width: 130rpx;
  height: 130rpx;
  opacity: 0.4;
  top: 0;
  left: 0;
}
.pages-mine .help-img-info .help-info {
  width: 130rpx;
  height: 130rpx;
  opacity: 1;
  top: 0;
  left: 0;
}
.pages-mine .help-img-info .help-info .help-img {
  width: 118rpx;
  height: 118rpx;
}
.pages-mine .help-img-info .help-info .help-img .iconfont {
  font-size: 44rpx;
  margin-bottom: 4rpx;
}

<block wx:if="{{isLoad}}"><view class="apply-pages"><view class="apply-form pl-lg pr-lg"><view class="fill-base mt-md radius-16"><view class="flex-between ml-lg mr-lg b-1px-b"><view class="item-text">联系人</view><input class="item-input flex-1" type="text" maxlength="20" placeholder="{{rule[0].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','user_name','$event',[]],['form']]]]]}}" value="{{form.user_name}}" bindinput="__e"/></view><view class="flex-between ml-lg mr-lg"><view class="item-text">手机号</view><input class="item-input flex-1" type="text" placeholder="{{rule[1].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e"/></view></view><view class="fill-base mt-md radius-16"><view class="flex-between ml-lg mr-lg b-1px-b"><view class="item-text">选择地区</view><view data-event-opts="{{[['tap',[['toChooseLocation',['$event']]]]]}}" class="item-input text flex-1" catchtap="__e"><view class="flex-y-center text-right"><view class="flex-1 text-right">{{form.address||'点击右边图标设置'}}</view><view class="iconfont iconjuli ml-sm _i" style="{{'color:'+(primaryColor)+';'}}"></view></view></view></view><view class="flex-between ml-lg mr-lg"><view class="item-text">详细地址</view><input class="item-input flex-1" type="text" placeholder="{{rule[3].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','address_info','$event',[]],['form']]]]]}}" value="{{form.address_info}}" bindinput="__e"/></view></view><view class="fill-base mt-md radius-16"><view class="flex-between ml-lg mr-lg"><view class="item-text flex-1" style="width:300rpx;">设为默认地址</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="item-input" catchtap="__e"><view class="{{['iconfont','icon-switch','_i',[(form.status==1)?'icon-switch-on':'']]}}" style="{{'color:'+(form.status==1?primaryColor:'#f2f2f2')+';'}}"></view></view></view></view><view class="f-icontext c-caption mt-md">设置后，下单时优先展示该地址</view></view><view class="space-max-footer"></view><fix-bottom-button vue-id="90a56f20-1" text="{{textArr}}" bgColor="#fff" data-event-opts="{{[['^cancel',[['toDel']]],['^confirm',[['submit']]]]}}" bind:cancel="__e" bind:confirm="__e" bind:__l="__l"></fix-bottom-button></view></block>
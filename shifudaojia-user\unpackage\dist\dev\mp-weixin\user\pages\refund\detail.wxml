<block wx:if="{{detail.id}}"><view class="order-pages"><view class="item-child mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-base radius-16" style="{{'background:'+(primaryColor)+';'}}"><view class="flex-y-baseline text-bold">{{statusType[detail.status]+''}}<block wx:if="{{detail.status==2}}"><view class="f-caption text-normal ml-md">{{"退款金额¥"+detail.refund_price}}</view></block></view></view><view class="address-info flex-warp mt-lg ml-lg mr-lg pd-lg fill-base radius-16"><view class="address-icon flex-center c-base radius" style="{{'background:'+('linear-gradient(to right, '+subColor+', '+primaryColor+')')+';'}}"><view class="iconfont iconjuli _i"></view></view><view class="flex-1 flex-between ml-md"><view class="max-540"><view class="flex-y-baseline username c-title text-bold">{{detail.address_info.user_name+''}}<view class="ml-md f-desc c-caption">{{detail.address_info.mobile}}</view></view><view class="f-desc c-title">{{''+(detail.address_info.address+' '+detail.address_info.address_info)+''}}</view></view></view></view><view class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16"><block wx:for="{{detail.order_goods}}" wx:for-item="aitem" wx:for-index="aindex" wx:key="aindex"><view class="flex-center mb-lg"><image class="avatar lg radius-16" mode="aspectFill" src="{{aitem.goods_cover}}"></image><view class="flex-1 ml-md"><view class="flex-between"><view class="{{['goods-title','f-title','c-title','ellipsis',[(aitem.refund_num>0)?'max-300':'']]}}">{{''+aitem.goods_name+''}}</view><block wx:if="{{aitem.refund_num>0}}"><view class="f-caption c-warning">{{"已退x"+aitem.refund_num}}</view></block></view><view class="f-caption c-caption mt-sm">{{"服务技师："+detail.coach_info.coach_name}}</view><view class="flex-between"><view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+aitem.goods_price+''}}</view></view><view class="c-paragraph">{{"x"+aitem.num}}</view></view></view></view></block><view class="flex-between"><veiw class="flex-1" vue-id="5173e15f-1" bind:__l="__l"></veiw><view class="flex-y-center f-desc c-title text-bold">共<view class="c-warning">{{detail.all_goods_num+''}}</view>件
				合计：<view class="f-icontext">¥</view>{{detail.apply_price}}</view></view><block wx:if="{{detail.status!=2}}"><view class="flex-between mt-lg"><view class="flex-1"></view><block wx:if="{{detail.status==3}}"><button data-event-opts="{{[['tap',[['toTel',['$event']]]]]}}" class="clear-btn order" catchtap="__e">联系平台</button></block><block wx:if="{{detail.status==1}}"><button data-event-opts="{{[['tap',[['toCancel',['$event']]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')}}" catchtap="__e">取消退款</button></block></view></block></view><view class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16"><view class="flex-between pb-lg f-title c-title text-bold b-1px-b">退款信息</view><view class="pt-lg f-desc"><view class="flex-between mb-md"><view>退款单号</view><view class="c-caption flex-y-center ml-md"><view class="max-400 ellipsis">{{detail.order_code}}</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="copy-btn radius-5 f-icontext ml-sm" bindtap="__e">复制</view></view></view><block wx:if="{{detail.out_refund_no}}"><view class="flex-between mb-md"><view>微信退款单号</view><view class="c-caption flex-y-center ml-md"><view>{{detail.out_refund_no}}</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="copy-btn radius-5 f-icontext ml-sm" bindtap="__e">复制</view></view></view></block><view class="flex-warp mb-md"><view>提交日期</view><view class="c-caption text-right flex-1 ml-md">{{detail.create_time}}</view></view><block wx:if="{{detail.status!=1}}"><view class="flex-warp mb-md"><view>审核日期</view><view class="c-caption text-right flex-1 ml-md">{{detail.refund_time}}</view></view></block><view class="flex-column mb-md"><view>退款原因</view><view class="c-caption mt-sm">{{detail.text}}</view></view><block wx:if="{{$root.g0}}"><view class="flex-column"><view>上传图片</view><view class="flex-warp"><block wx:for="{{detail.imgs}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><image class="refund-img mt-md mr-md radius-10" src="{{item}}" data-event-opts="{{[['tap',[['previewImage',['$0','$1'],[[['detail.imgs','',index]],'detail.imgs']]]]]}}" bindtap="__e"></image></block></block></view></view></block></view></view><view class="space-footer"></view><common-popup class="vue-ref" vue-id="5173e15f-2" type="CANCEL_REFUND_ORDER" info="{{popupInfo}}" data-ref="cancel_item" data-event-opts="{{[['^confirm',[['confirmCancel']]]]}}" bind:confirm="__e" bind:__l="__l"></common-popup></view></block>
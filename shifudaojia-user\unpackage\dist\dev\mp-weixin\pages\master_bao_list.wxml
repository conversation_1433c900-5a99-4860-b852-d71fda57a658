<view class="page data-v-68265041"><view class="main data-v-68265041"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="main_item_already data-v-68265041"><view class="title data-v-68265041">{{item.$orig.pay_type==-2?'等待客户选择':'客户已选择报价'}}</view><view class="ok data-v-68265041">您已报价</view><view class="no data-v-68265041">{{"单号："+item.$orig.order_code}}</view><view class="mid data-v-68265041"><view class="lef data-v-68265041"><image src="{{item.$orig.goods_cover}}" mode class="data-v-68265041"></image><text class="data-v-68265041">{{item.$orig.goods_name}}</text></view></view><view class="bot data-v-68265041"><text class="data-v-68265041">{{item.g0}}</text></view><view class="shifu data-v-68265041"><scroll-view scroll-x="true" class="data-v-68265041"><view class="shifu_item data-v-68265041"><view class="top data-v-68265041"><image src="{{item.$orig.self_img}}" mode class="data-v-68265041"></image><view class="info data-v-68265041"><view class="name data-v-68265041">{{item.$orig.coach_name}}</view></view></view><text class="data-v-68265041">{{"￥"+item.$orig.price}}</text></view></scroll-view></view><block wx:if="{{item.$orig.pay_type==-2}}"><view class="btnbox data-v-68265041"><view data-event-opts="{{[['tap',[['cancelBao',['$0'],[[['list','',index]]]]]]]}}" class="btn can data-v-68265041" bindtap="__e">取消报价</view><view data-event-opts="{{[['tap',[['againBao',['$0'],[[['list','',index]]]]]]]}}" class="btn re data-v-68265041" bindtap="__e">重新报价</view></view></block></view></block></view><u-popup vue-id="c6b78e36-1" show="{{show}}" round="{{10}}" closeable="{{true}}" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-68265041" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-68265041"><view class="title data-v-68265041">重新报价</view><view class="title2 data-v-68265041">报价金额</view><view class="money data-v-68265041"><u--input bind:input="__e" vue-id="{{('c6b78e36-2')+','+('c6b78e36-1')}}" placeholder="请输入报价金额" prefixIcon="rmb" prefixIconStyle="font-size: 22px;color: #909399" type="number" value="{{input}}" data-event-opts="{{[['^input',[['__set_model',['','input','$event',[]]]]]]}}" class="data-v-68265041" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['confirmBao',['$event']]]]]}}" class="btn data-v-68265041" bindtap="__e">确认报价</view></view></u-popup><u-modal vue-id="c6b78e36-3" show="{{showCancel}}" title="取消报价" content="确定要取消对本单的报价吗？" showCancelButton="{{true}}" data-event-opts="{{[['^close',[['e0']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-68265041" bind:__l="__l"></u-modal></view>
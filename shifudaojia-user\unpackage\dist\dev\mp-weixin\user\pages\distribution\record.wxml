<block wx:if="{{isLoad}}"><view class="master-income-record"><view class="mine-menu-list c-base" style="{{'background:'+(primaryColor)+';'}}"><view class="space-lg"></view><view class="space-lg"></view><view class="flex-center f-caption mt-sm mb-sm">已累计提现金额(元)</view><view class="money-info flex-center flex-y-baseline">¥<view class="money">{{list.extract_total_price+''}}</view></view><view class="space-md"></view><view class="space-lg"></view></view><view class="fill-base"><tab vue-id="186948aa-1" list="{{tabList}}" activeIndex="{{activeIndex*1}}" activeColor="{{primaryColor}}" width="25%" height="100rpx" data-event-opts="{{[['^change',[['handerTabChange']]]]}}" bind:change="__e" bind:__l="__l"></tab><view class="ml-lg mr-lg b-1px-b"></view><block wx:for="{{list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['list-item','flex-center','pd-lg',[(index!=0)?'b-1px-t':'']]}}"><view class="flex-warp flex-1"><view class="item-tag mt-sm mr-md radius" style="{{'background:'+(item.status==1?'#11C95E':item.status==2?primaryColor:subColor)+';'}}"></view><view class="f-caption c-caption"><view class="f-title c-title text-bold">{{statusType[item.status]}}</view><block wx:if="{{item.status==2}}"><view class="f-caption c-caption">{{"实际到账："+item.apply_price}}</view></block><view class="f-caption c-caption">{{item.create_time}}</view></view></view><view class="f-md-title">{{(item.status==2?'+':'-')+item.total_price}}</view></view></block></view><block wx:if="{{loading}}"><load-more vue-id="186948aa-2" noMore="{{$root.g0}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g1}}"><abnor vue-id="186948aa-3" bind:__l="__l"></abnor></block><view class="space-footer"></view></view></block>
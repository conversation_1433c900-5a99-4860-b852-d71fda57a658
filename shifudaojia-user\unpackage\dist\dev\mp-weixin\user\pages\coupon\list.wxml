<view class="user-coupon-list"><fixed vue-id="5e3e2e40-1" bind:__l="__l" vue-slots="{{['default']}}"><tab vue-id="{{('5e3e2e40-2')+','+('5e3e2e40-1')}}" list="{{tabList}}" activeIndex="{{activeIndex*1}}" activeColor="{{primaryColor}}" width="33.3%" height="100rpx" data-event-opts="{{[['^change',[['handerTabChange']]]]}}" bind:change="__e" bind:__l="__l"></tab><view class="b-1px-b"></view></fixed><block wx:for="{{list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',[index]]]]]}}" class="{{['list-item','rel','mt-md','ml-lg','mr-lg','fill-base','radius-16',[(activeIndex>0)?'grayscale':'']]}}" catchtap="__e"><view class="item-title flex-center pl-md pr-md abs f-paragraph c-warning">{{item.title}}</view><view class="mt-lg pd-lg"><view class="flex-between"><view class="f-caption c-caption"><view class="flex-y-baseline"><view class="flex-y-baseline c-warning">¥<view class="f-md-title">{{item.discount}}</view></view><view class="ml-sm">{{item.type==0?'满'+item.full+'元可用':'无门槛'}}</view></view><view>{{"数量：x"+item.num}}</view></view><view class="use-btn flex-center f-caption radius" style="{{'color:'+(primaryColor)+';'+('background:'+(activeIndex>0?'#eee':'')+';')+('border:'+(activeIndex>0?'1rpx solid #eee':'1rpx solid '+primaryColor)+';')}}">{{''+statusType[item.status]+''}}</view></view><view class="f-caption c-caption mt-sm">{{"有效期："+item.start_time}}</view></view><view data-event-opts="{{[['tap',[['toShowItem',[index]]]]]}}" class="flex-between fill-space f-caption c-caption pt-md pb-md pl-lg pr-lg b-1px-t" catchtap="__e"><view>查看详情</view><view class="{{['iconfont','icon-right','_i',[(!item.is_show)?'rotate-90':''],[(item.is_show)?'rotate-270':'']]}}"></view></view><block wx:if="{{item.is_show}}"><view class="pl-lg pr-lg pb-lg fill-space f-desc c-caption"><view class="flex-warp"><view>使用规则：</view><view class="flex-1"><text style="word-break:break-all;" decode="emsp">{{item.rule}}</text></view></view><view class="flex-warp mt-md"><view>优惠详情：</view><view class="flex-1"><text style="word-break:break-all;" decode="emsp">{{item.text}}</text></view></view><view class="flex-warp mt-md"><view>限用服务：</view><view class="flex-1"><block wx:for="{{item.service}}" wx:for-item="aitem" wx:for-index="aindex" wx:key="*this"><view class="{{[[(aindex!=0)?'mt-sm':'']]}}">{{''+aitem.title+''}}</view></block></view></view></view></block></view></block><block wx:if="{{loading}}"><load-more vue-id="5e3e2e40-3" noMore="{{$root.g0}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g1}}"><abnor vue-id="5e3e2e40-4" type="COUPON" bind:__l="__l"></abnor></block><view class="space-footer"></view></view>
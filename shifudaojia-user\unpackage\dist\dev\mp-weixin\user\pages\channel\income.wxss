@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-channel-index .mine-menu-list {
  height: 235rpx;
}
.user-channel-index .mine-menu-list .channel-code {
  width: 134rpx;
  height: 54rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100rpx 0 0 100rpx;
}
.user-channel-index .mine-menu-list .channel-code .iconerweima {
  font-size: 28rpx;
  margin: 0 5rpx;
}
.user-channel-index .mine-menu-list .channel-code .icon-down {
  font-size: 18rpx;
  -webkit-transform: scale(0.5) rotate(270deg);
          transform: scale(0.5) rotate(270deg);
}
.user-channel-index .mine-menu-list .money-count .item-child {
  width: 50%;
}
.user-channel-index .mine-menu-list .money-count .item-child .num {
  font-size: 49rpx;
}
.user-channel-index .mine-menu-list .money-count .line {
  width: 1rpx;
  height: 47rpx;
  background: rgba(255, 255, 255, 0.3);
}
.user-channel-index .search-info {
  border-radius: 0 0 16rpx 16rpx;
  overflow: hidden;
}
.user-channel-index .search-info .item-search {
  width: 630rpx;
}
.user-channel-index .search-info .iconshaixuanxia-1 {
  font-size: 20rpx;
  color: #CDCDCD;
  -webkit-transform: scale(0.65);
          transform: scale(0.65);
}
.user-channel-index .popup-rank {
  border-radius: 34rpx 34rpx 0 0;
}
.user-channel-index .popup-rank .item-rank {
  width: 157rpx;
  height: 72rpx;
  border: 1px solid #E5E5E5;
}
.user-channel-index .popup-rank .btn-info {
  background: #F9F9F9;
}
.user-channel-index .popup-rank .btn-info .item-child {
  width: 320rpx;
  height: 80rpx;
  background: #FFFFFF;
  border: 1rpx solid #C7C7C7;
  margin: 0 14rpx;
}
.user-channel-index .popup-rank .space-safe {
  background: #F9F9F9;
}

<template>
	<view class="mine-pages-setting">
		<!-- #ifdef APP-ANDROID -->
		<u-popup :show="showImg" mode='top' :round="10">
			<view style="padding: 20rpx 100rpx; background-color: #2a82e4; color: #fff;border-radius:20rpx;">
				<text>使用摄像头中拍摄照片或相册中选择图片用以上传用户所需的图片</text>
			</view>
		</u-popup>
		<!-- #endif -->
		<u-modal :show="show1" title="修改密码" showCancelButton @confirm="confirmmm" @cancel="cancelmm">
			<view class="">
				<view class="" style="margin-bottom: 10rpx;">
					<u--input prefixIcon="lock" placeholder="输入密码" v-model="newpassword" type="password"></u--input>
				</view>
				<view class="" style="margin-bottom: 10rpx;">
					<u--input prefixIcon="lock" placeholder="再次输入密码" v-model="againpassword" type="password"></u--input>
				</view>
				<view class="" style="margin-bottom: 10rpx;">
					<u-input prefixIcon="tags" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入验证码"
						border="bottom" v-model="yzmcode" style="margin-top: 84rpx;">
						<template slot="suffix">
							<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
							<u-button @tap="getCode" :text="tips" type="success" size="mini"></u-button>
						</template>
					</u-input>
				</view>
			</view>
		</u-modal>
		<u-modal :show="show" title="注销账号" content='确定要注销本账号吗(注销后账号无法恢复,个人数据将被清空)' showCancelButton @confirm="confirm"
			@cancel="cancel"></u-modal>
		<view class="flex-center flex-column pd-lg fill-base">
			<view class="space-lg"></view>
			<view class="space-lg"></view>
			<upload @upload="imgUpload" :imagelist="user_info.avatarUrl" imgtype="avatarUrl" :imgsize="1" radius
				@changeShow="changeShow">
			</upload>
			<!-- <button style="border-radius: 50%;" open-type="chooseAvatar" @chooseavatar="chooseAvatar">
				<image :src="user_info.avatarUrl" mode="" style="width: 200rpx;height: 200rpx;border-radius: 50%;">
				</image>
			</button> -->
			<!-- <image class="avatar radius" :src="user_info.avatarUrl"></image> -->
			<!-- <view class="f-title c-caption mt-md" v-if="user_info.phone"> {{user_info.split_phone}} </view> -->
		</view>
		<view class="flex-between pd-lg fill-base f-paragraph">
			<view>昵称</view>
			<!-- <view class="c-caption">{{user_info[item.key]}}</view> -->
			<input type="nickname" v-model="user_info.nickName" style="text-align: right;color: #ADADAD;max-width: 400rpx;"
				@blur="bindblur" @input="bindinput" >
		</view>
		<view class="flex-between pd-lg fill-base f-paragraph">
			<view>注册时间</view>
			<!-- <view class="c-caption">{{user_info[item.key]}}</view> -->
			<input type="text" v-model="user_info.create_date" style="text-align: right;color: #ADADAD;max-width: 400rpx;" disabled>
		</view>
		<view class="flex-between pd-lg fill-base f-paragraph">
			<view>手机号</view>
			<!-- <view class="c-caption">{{user_info[item.key]}}</view> -->
			<button open-type="getPhoneNumber" @getphonenumber="authPhone" style="color: #ADADAD;">
				{{user_info.phone}}
			</button>
		</view>

		<view class="flex-between pd-lg fill-base f-paragraph" @click="editpassword">
			<view>修改密码</view>
			<!-- <view class="c-caption">{{user_info[item.key]}}</view> -->
			<!-- <view class=""></view> -->
		</view>

		<!-- <view class="space-md"></view> -->
		<view @tap.stop="goDetail(index,'infoList')" class="flex-between pd-lg fill-base f-paragraph"
			v-for="(item,index) in infoList" :key="index">
			<view>{{item.text}}</view>
			<i class="iconfont icon-right"></i>
		</view>
		<!-- #ifdef APP-PLUS -->
		<view class="pd-lg fill-base f-paragraph" @tap="over">账号注销</view>
		<!-- #endif -->
		<view class="" style="display: flex;justify-content: space-around;align-items: center;margin-top: 10rpx;">
			<!-- <button style="background-color: #cb2d01;width: 250rpx;border-radius: 20rpx;"
				@click="disabled = false">编辑</button> -->
			<button
				style="background-color: #2367c7;width: 200rpx;border-radius: 20rpx;height: 80rpx;line-height: 80rpx;"
				@click="save">保存</button>
		</view>
		<!-- <view class="space-max-footer"></view> -->


		<!-- #ifdef APP-PLUS -->
		<fix-bottom-button @confirm="toLoginOut" :text="[{ text: '退出登录', type: 'confirm' }]" bgColor="#fff">
		</fix-bottom-button>
		<!-- #endif -->

	</view>
</template>

<script>
	import {
		mapState,
		mapActions,
		mapMutations
	} from "vuex"
	import siteInfo from '@/siteinfo.js';
	export default {
		data() {
			return {
				// disabled: true,
				showImg: false,
				tips: '',
				yzmcode: '',
				againpassword: '',
				newpassword: '',
				show1: false,
				isLoad: false,
				show: false,
				options: {},
				userInfoList: [{
					text: '昵称',
					key: 'nickName'
				}, {
					text: '注册时间',
					key: 'create_date',
					dis: true
				}, {
					text: '手机号',
					key: 'phone'
				}],
				infoList: [{
					text: '用户隐私协议',
					url: 1
				}, {
					text: '个人信息保护指引',
					url: 2
				}],
				user_info: {}
			}
		},
		computed: mapState({
			primaryColor: state => state.config.configInfo.primaryColor,
			subColor: state => state.config.configInfo.subColor,
			userInfo: state => state.user.userInfo,
		}),
		async onLoad() {
			this.initIndex()
		},
		methods: {
			...mapActions(['getUserInfo', 'getAuthPhone']),
			...mapMutations(['updateUserItem']),
			changeShow(e) {
				this.showImg = e
			},
			cancelmm() {
				this.newpassword = '',
					this.againpassword = '',
					this.yzmcode = '',
					this.show1 = false
			},
			async confirmmm() {
				if (this.newpassword == '') {
					return uni.showToast({
						icon: 'none',
						title: '请填写密码'
					})
				}
				if (this.againpassword == '') {
					return uni.showToast({
						icon: 'none',
						title: '请填写确认密码'
					})
				}
				if (this.yzmcode == '') {
					return uni.showToast({
						icon: 'none',
						title: '请填写验证码'
					})
				}
				if (this.newpassword != this.againpassword) {
					return uni.showToast({
						icon: 'none',
						title: '两次密码输入不一致'
					})
				}
				const res = await this.$api.service.forget({
					short_code: this.yzmcode,
					phone: this.user_info.phone,
					password: this.newpassword
				})
				// uni.showModal({
				// 	title:res
				// })
				// console.log(res);
				if (res.length == 0) {
					uni.showToast({
						title: "修改成功",
						icon: 'success'
					})
					this.show1 = false
				}
			},
			async getCode() {
				if (this.user_info.phone === '') {
					uni.showToast({
						icon: 'none',
						title: '请先绑定手机号'
					})
					return
				}
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					const res = await this.$api.service.getShortCode({
						phone: this.user_info.phone
					})
					// console.log(res);
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			codeChange(text) {
				this.tips = text;
			},

			editpassword() {
				this.show1 = true
			},
			cancel() {
				this.show = false
			},
			async confirm() {
				const res = await this.$api.service.cancellation()
				console.log(res);
				try {
					uni.clearStorageSync();
					uni.showToast({
						icon: 'none',
						title: '注销账号成功'
					})
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/login'
						})
					}, 1000)
				} catch (e) {
					// error
				}
			},
			async initIndex() {
				this.$util.setNavigationBarColor({
					bg: '#599eff'
				})
				this.user_info = this.$util.deepCopy(this.userInfo)
				this.user_info.avatarUrl = [{
					path: this.user_info.avatarUrl
				}]
			},
			over() {
				this.show = true
			},
			save() {
				this.$api.service.getSaveInfo({
					nickName: this.user_info.nickName,
					phone: this.user_info.phone,
					avatarUrl: this.user_info.avatarUrl[0].path
				}).then(res => {
					uni.showToast({
						icon: 'success',
						title: '保存成功'
					})
					this.$store.dispatch('getUserInfo')
					uni.$emit('refreshInfo')
				})
			},
			chooseAvatar(e) {
				this.user_info.avatarUrl = e.detail.avatarUrl
			},
			async authPhone(e) {
				let phone = await this.getAuthPhone({
					e,
				})
				this.user_info.phone = phone
			},
			bindblur(e) {
				// console.log(e);
				this.user_info.nickName = e.detail.value; // 获取微信昵称
			},
			bindinput(e) {
				// console.log(e);
				this.user_info.nickName = e.detail.value; //这里要注意如果只用blur方法的话用户在输入玩昵称后直接点击保存按钮，会出现修改不成功的情况。
			},
			imgUpload(e) {
				let {
					imagelist,
					imgtype
				} = e;
				this.user_info[imgtype] = imagelist;
			},
			goDetail(index, key) {
				let {
					siteroot
				} = siteInfo
				let {
					url
				} = this[key][index]
				if (key == 'infoList') {
					// #ifdef MP-WEIXIN
					this.$util.goUrl({
						url: `/user/pages/info?type=${url}`
					})
					// #endif
					// #ifndef MP-WEIXIN

					let href = siteroot.split('index.php')[0]
					let page = url == 1 ? 'protocol' : 'information'
					url = `${href}${page}.html`
					this.$util.goUrl({
						url,
						openType: 'web'
					})
					// #endif


				}

			},
			toLoginOut() {
				let arr = ['autograph', 'userInfo', 'location', 'appLogin']
				arr.map(key => {
					uni.setStorageSync(key, '')
					this.updateUserItem({
						key,
						val: ''
					})
				})
				this.$util.showToast({
					title: `退出登录`
				})
				setTimeout(() => {
					let url = `/pages/service`
					// #ifdef APP-PLUS
					url = `/pages/login`
					// #endif
					this.$util.goUrl({
						url,
						openType: `reLaunch`
					})
				}, 1000)
			}

		}
	}
</script>


<style lang="scss">
	.mine-pages-setting {
		.iconfont {
			color: #999
		}
	}
</style>
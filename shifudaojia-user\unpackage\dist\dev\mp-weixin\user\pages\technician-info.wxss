@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.technician-info .avatar {
  width: 148rpx;
  height: 148rpx;
  border: 2rpx solid #fff;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
  top: -32rpx;
  left: 32rpx;
}
.technician-info .sex-tag {
  width: 80rpx;
  min-width: 80rpx;
  height: 40rpx;
  border: 1px solid #CCCCCC;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
}
.technician-info .sex-tag .iconfont {
  margin-right: 8rpx;
  font-size: 28rpx;
}
.technician-info .line {
  width: 2rpx;
  height: 20rpx;
  background: #ccc;
}
.technician-info .img-list {
  margin-top: 15rpx;
}
.technician-info .img-list .img-item {
  width: 305rpx;
  height: 190rpx;
  border: 1rpx solid #f4f6f8;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
  margin-top: 16rpx;
}
.technician-info .img-list .img-item .img {
  width: 300rpx;
  height: 190rpx;
}
.technician-info .img-list .img-item:nth-child(2n-1) {
  margin-right: 16rpx;
}
.technician-info .img-list .img-item.abs {
  top: -16rpx;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}
.technician-info .img-list .img-item-mini {
  width: 199rpx;
  height: 199rpx;
  margin-top: 16rpx;
}
.technician-info .img-list .img-item-mini:nth-child(3n-1) {
  margin-right: 16rpx;
  margin-left: 16rpx;
}
.technician-info .item-video {
  width: 630rpx;
  height: 380rpx;
  display: block;
}

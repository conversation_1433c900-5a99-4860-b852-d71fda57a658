<view style="background-color:#F4F6F7;padding:20rpx 0;"><view class="hideCanvasView"><l-painter class="hideCanvas vue-ref" vue-id="08c0082f-1" data-ref="painter" bind:__l="__l"></l-painter></view><block wx:if="{{src}}"><block><image class="code-img" src="{{src}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="info_text f_c"><view style="font-size:28rpx;margin-bottom:20rpx;color:#2F2F2F;">邀请3步</view><view class="f_r_sb_c" style="font-size:28rpx;color:#767676;"><view class="f_r_m_c">1.分享海报</view><image class="info_text_img" src="/static/coupon/btn.png" mode></image><view class="f_r_m_c">2.微信扫码</view><image class="info_text_img" src="/static/coupon/btn.png" mode></image><view class="f_r_m_c">3.注册登录</view></view></view><view class="btns"><button data-event-opts="{{[['tap',[['saveImage',['$event']]]]]}}" class="save-btn flex-center radius" style="{{'background:'+(primaryColor)+';'}}" bindtap="__e">保存图片至相册</button></view></block></block></view>
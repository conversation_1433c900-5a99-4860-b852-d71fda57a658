@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-00b0e8cf {
  background-color: #F8F8F8;
  height: 100vh;
  overflow: auto;
  padding-top: 100rpx;
}
.page .header.data-v-00b0e8cf {
  position: fixed;
  top: 0;
  width: 750rpx;
  height: 100rpx;
  background: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.page .header .header_item.data-v-00b0e8cf {
  max-width: 85rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.page .header .header_item .blue.data-v-00b0e8cf {
  margin-top: 8rpx;
  width: 38rpx;
  height: 6rpx;
  background: #2E80FE;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
}
.page .main.data-v-00b0e8cf {
  padding: 40rpx 30rpx;
}
.page .main .main_item.data-v-00b0e8cf {
  width: 690rpx;
  height: 284rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 28rpx 36rpx;
  margin-bottom: 20rpx;
}
.page .main .main_item .head.data-v-00b0e8cf {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item .head .no.data-v-00b0e8cf {
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid.data-v-00b0e8cf {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .mid .lef.data-v-00b0e8cf {
  display: flex;
  align-items: center;
}
.page .main .main_item .mid .lef image.data-v-00b0e8cf {
  width: 120rpx;
  height: 120rpx;
}
.page .main .main_item .mid .lef text.data-v-00b0e8cf {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid .righ.data-v-00b0e8cf {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
}
.page .main .main_item .bot.data-v-00b0e8cf {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .bot .qzf.data-v-00b0e8cf,
.page .main .main_item .bot .qxdd.data-v-00b0e8cf,
.page .main .main_item .bot .lxsf.data-v-00b0e8cf,
.page .main .main_item .bot .qrwc.data-v-00b0e8cf,
.page .main .main_item .bot .qpl.data-v-00b0e8cf {
  width: 148rpx;
  height: 48rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 48rpx;
  text-align: center;
}
.page .main .main_item .bot .qzf.data-v-00b0e8cf,
.page .main .main_item .bot .qrwc.data-v-00b0e8cf,
.page .main .main_item .bot .qpl.data-v-00b0e8cf {
  color: #fff;
}
.page .main .main_item .bot .qxdd.data-v-00b0e8cf,
.page .main .main_item .bot .lxsf.data-v-00b0e8cf {
  background: #FFFFFF;
  border: 2rpx solid #2E80FE;
  color: #2E80FE;
}
.page .main .main_item_already.data-v-00b0e8cf {
  padding: 28rpx 36rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
}
.page .main .main_item_already .title.data-v-00b0e8cf {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
}
.page .main .main_item_already .ok.data-v-00b0e8cf {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #E72427;
}
.page .main .main_item_already .no.data-v-00b0e8cf {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .mid.data-v-00b0e8cf {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item_already .mid .lef.data-v-00b0e8cf {
  display: flex;
  align-items: center;
}
.page .main .main_item_already .mid .lef image.data-v-00b0e8cf {
  width: 120rpx;
  height: 120rpx;
}
.page .main .main_item_already .mid .lef text.data-v-00b0e8cf {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .mid .righ.data-v-00b0e8cf {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
}
.page .main .main_item_already .bot.data-v-00b0e8cf {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item_already .shifu.data-v-00b0e8cf {
  margin-top: 20rpx;
}
.page .main .main_item_already .shifu scroll-view.data-v-00b0e8cf {
  width: 100%;
  white-space: nowrap;
}
.page .main .main_item_already .shifu scroll-view .shifu_item.data-v-00b0e8cf {
  display: inline-block;
  margin-right: 28rpx;
}
.page .main .main_item_already .shifu scroll-view .shifu_item image.data-v-00b0e8cf {
  width: 92rpx;
  height: 92rpx;
  border-radius: 50%;
}
.page .main .main_item_already .shifu scroll-view .shifu_item text.data-v-00b0e8cf {
  font-size: 22rpx;
  font-weight: 500;
  color: #E72427;
  text-align: center;
}
.page .main .main_item_already .tips.data-v-00b0e8cf {
  width: 100%;
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  margin-top: 20rpx;
  text-align: right;
}

<view class="order-pages"><view class="fix" style="top:-100%;left:-100%;"><block wx:for="{{unix_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><min-countdown vue-id="{{'f4bfb162-1-'+index}}" targetTime="{{item.start_service_time_unix*1000}}" isPlay="{{true}}" bind:__l="__l"></min-countdown></block></view><fixed vue-id="f4bfb162-2" bind:__l="__l" vue-slots="{{['default']}}"><tab vue-id="{{('f4bfb162-3')+','+('f4bfb162-2')}}" list="{{tabList}}" activeIndex="{{activeIndex*1}}" activeColor="{{primaryColor}}" width="{{100/$root.g0+'%'}}" height="100rpx" data-event-opts="{{[['^change',[['handerTabChange']]]]}}" bind:change="__e" bind:__l="__l"></tab><view class="b-1px-b"></view></fixed><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',[index]]]]]}}" class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16" catchtap="__e"><view data-event-opts="{{[['tap',[['goShop',[index]]]]]}}" class="flex-between pb-lg" catchtap="__e"><view class="f-paragraph c-title max-500 ellipsis">{{"订单号："+item.$orig.order_code}}</view><view class="f-caption text-bold" style="{{'color:'+(item.$orig.pay_type==2?primaryColor:item.g1?subColor:item.$orig.pay_type==6?'#11C95E':'#333')+';'}}">{{''+statusType[item.$orig.pay_type]+''}}</view></view><block wx:for="{{item.$orig.order_goods}}" wx:for-item="aitem" wx:for-index="aindex" wx:key="aindex"><view class="flex-center mb-lg"><image class="avatar lg radius-16" mode="aspectFill" src="{{aitem.goods_cover}}"></image><view class="flex-1 ml-md"><view class="flex-between"><view class="{{['goods-title','f-title','c-title','ellipsis',[(aitem.refund_num>0)?'max-300':'']]}}">{{''+aitem.goods_name+''}}</view><view class="flex-center"><block wx:if="{{aitem.refund_num>0}}"><view class="mr-sm f-caption c-warning">{{"已退x"+aitem.refund_num}}</view></block><block wx:if="{{aitem.is_deposit_mode}}"><view class="time-long2 mt-md flex-center">定 金</view></block></view></view><view class="f-caption c-caption mt-md">{{"服务时间："+item.$orig.start_time}}</view><view class="flex-between"><view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+aitem.price+''}}</view></view><view class="c-paragraph">{{"x"+aitem.num}}</view></view></view></view></block><view class="flex-between pt-lg b-1px-t"><view class="flex-y-center f-desc c-title">总计：<view class="f-paragraph c-warning text-bold">{{"¥"+item.$orig.pay_price}}</view></view><view class="flex-warp"><block wx:if="{{item.$orig.pay_type==2}}"><block><button data-event-opts="{{[['tap',[['toRefuse',[index]]]]]}}" class="clear-btn order" catchtap="__e">拒绝接单</button><button data-event-opts="{{[['tap',[['toConfirm',[index,3]]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')+('border-color:'+(primaryColor)+';')}}" catchtap="__e">确认接单</button></block></block><block wx:if="{{item.$orig.pay_type==3||item.$orig.pay_type==4||item.$orig.pay_type==5}}"><block><button data-event-opts="{{[['tap',[['toTel',[index]]]]]}}" class="clear-btn order" catchtap="__e">咨询</button><button data-event-opts="{{[['tap',[['toConfirm',[index,item.$orig.pay_type*1+1]]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')+('border-color:'+(primaryColor)+';')}}" catchtap="__e">{{item.$orig.pay_type==3?'已出发':item.$orig.pay_type==4?'拍照确认到达':'开始服务'}}</button></block></block><block wx:if="{{item.$orig.pay_type==6}}"><block><button data-event-opts="{{[['tap',[['toConfirm',[index,7]]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')+('border-color:'+(primaryColor)+';')}}" catchtap="__e">拍照完成服务</button></block></block></view></view></view></block><block wx:if="{{loading}}"><load-more vue-id="f4bfb162-4" noMore="{{$root.g2}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g3}}"><abnor vue-id="f4bfb162-5" bind:__l="__l"></abnor></block><view class="space-footer"></view><uni-popup class="vue-ref" vue-id="f4bfb162-6" type="center" custom="{{true}}" data-ref="refuse_item" bind:__l="__l" vue-slots="{{['default']}}"><view class="common-popup-content fill-base pd-lg radius-34"><view class="title">拒绝接单</view><view class="desc">请确认是否拒接接单</view><textarea class="pd-lg textarea f-desc c-title mt-lg radius-20" maxlength="200" placeholder="请输入拒单原因" placeholder-class="f-desc c-caption" data-event-opts="{{[['input',[['__set_model',['','coach_refund_text','$event',[]]]]]]}}" value="{{coach_refund_text}}" bindinput="__e"></textarea><view class="flex-center mt-md" style="width:540rpx;"><view class="flex-1"></view><view>{{($root.g4>200?200:$root.g5)+"/200"}}</view></view><view class="button"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="item-child" catchtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmRefuse',['$event']]]]]}}" class="item-child c-base" style="{{'background:'+(primaryColor)+';'+('color:'+('#fff')+';')}}" catchtap="__e">确定</view></view></view></uni-popup></view>
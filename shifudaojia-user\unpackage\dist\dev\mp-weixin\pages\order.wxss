@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-42e2fba5 {
  background-color: #F8F8F8;
  height: 100vh;
  overflow: auto;
  padding: 40rpx 0;
  padding-bottom: 132rpx;
}
.page .car_item.data-v-42e2fba5 {
  margin: 0 auto;
  width: 686rpx;
  height: 396rpx;
  background: #FFFFFF;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
  position: relative;
}
.page .car_item .trash.data-v-42e2fba5 {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}
.page .car_item .top.data-v-42e2fba5 {
  padding: 36rpx 0;
  display: flex;
  border-bottom: 2rpx solid #F2F3F6;
}
.page .car_item .top image.data-v-42e2fba5 {
  width: 200rpx;
  height: 200rpx;
}
.page .car_item .top .right.data-v-42e2fba5 {
  flex: 1;
  margin-left: 20rpx;
}
.page .car_item .top .right .name.data-v-42e2fba5 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
}
.page .car_item .top .right .choose.data-v-42e2fba5 {
  padding: 0 12rpx;
  margin-top: 12rpx;
  width: -webkit-fit-content;
  width: fit-content;
  height: 46rpx;
  background: #F8F8F8;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  display: flex;
  align-items: center;
}
.page .car_item .top .right .price.data-v-42e2fba5 {
  width: 100%;
  margin-top: 68rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: #E72427;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .car_item .bottom.data-v-42e2fba5 {
  height: 100rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.page .car_item .bottom .btn.data-v-42e2fba5 {
  width: 240rpx;
  height: 80rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 80rpx;
  text-align: center;
}

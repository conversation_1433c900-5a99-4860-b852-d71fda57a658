<view class="order-pages"><fixed vue-id="19ca71b2-1" bind:__l="__l" vue-slots="{{['default']}}"><tab vue-id="{{('19ca71b2-2')+','+('19ca71b2-1')}}" list="{{tabList}}" activeIndex="{{activeIndex*1}}" activeColor="{{primaryColor}}" width="25%" height="100rpx" data-event-opts="{{[['^change',[['handerTabChange']]]]}}" bind:change="__e" bind:__l="__l"></tab><view class="b-1px-b"></view></fixed><block wx:for="{{list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',[index]]]]]}}" class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16" catchtap="__e"><view class="flex-between pb-lg"><view class="f-paragraph c-title max-380 ellipsis">{{"订单号："+item.order_code}}</view><view class="f-caption text-bold" style="{{'color:'+(item.status==1?subColor:item.status==2?'#11C95E':'#333')+';'}}">{{''+statusType[item.status]+''}}</view></view><block wx:for="{{item.order_goods}}" wx:for-item="aitem" wx:for-index="aindex" wx:key="aindex"><view class="flex-center mb-lg"><image class="avatar lg radius-16" mode="aspectFill" src="{{aitem.goods_cover}}"></image><view class="flex-1 ml-md"><view class="flex-between"><view class="{{['goods-title','f-title','c-title','ellipsis',[(aitem.refund_num>0)?'max-300':'']]}}">{{''+aitem.goods_name+''}}</view><block wx:if="{{aitem.refund_num>0}}"><view class="f-caption c-warning">{{"已退x"+aitem.refund_num}}</view></block></view><view class="f-caption c-caption mt-sm">{{"服务技师："+item.coach_info.coach_name}}</view><view class="flex-between"><view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+aitem.goods_price+''}}</view></view><view class="c-paragraph">{{"x"+aitem.num}}</view></view></view></view></block><view class="flex-between pt-lg b-1px-t"><view class="flex-y-center f-desc c-title">合计：<view class="c-warning text-bold">{{"¥"+item.apply_price}}</view></view><view class="flex-warp"><block wx:if="{{item.status==1}}"><block><button data-event-opts="{{[['tap',[['toCancel',[index]]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')}}" catchtap="__e">取消退款</button></block></block><block wx:if="{{item.status==2}}"><view>{{"退款金额¥"+item.refund_price}}</view></block><block wx:if="{{item.status==3}}"><block><button data-event-opts="{{[['tap',[['toTel',['$event']]]]]}}" class="clear-btn order" catchtap="__e">联系平台</button></block></block></view></view></view></block><block wx:if="{{loading}}"><load-more vue-id="19ca71b2-3" noMore="{{$root.g0}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g1}}"><abnor vue-id="19ca71b2-4" bind:__l="__l"></abnor></block><view class="space-footer"></view><common-popup class="vue-ref" vue-id="19ca71b2-5" type="CANCEL_REFUND_ORDER" info="{{popupInfo}}" data-ref="cancel_item" data-event-opts="{{[['^confirm',[['confirmCancel']]]]}}" bind:confirm="__e" bind:__l="__l"></common-popup></view>
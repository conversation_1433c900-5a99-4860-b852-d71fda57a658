@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-53c348c8 {
  height: 100vh;
  overflow: auto;
  padding-top: 278rpx;
  position: relative;
  padding-bottom: 172rpx;
}
.page .header.data-v-53c348c8 {
  width: 750rpx;
  height: 376rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -9999;
}
.page .header image.data-v-53c348c8 {
  width: 100%;
  height: 100%;
}
.page .card.data-v-53c348c8 {
  margin-left: 32rpx;
  width: 686rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  padding: 40rpx;
}
.page .card .top.data-v-53c348c8 {
  padding-bottom: 40rpx;
  border-bottom: 2rpx solid #F2F3F6;
}
.page .card .top .title.data-v-53c348c8 {
  font-size: 36rpx;
  font-weight: 500;
  color: #171717;
  letter-spacing: 2rpx;
}
.page .card .top .price.data-v-53c348c8 {
  margin-top: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #E72427;
}
.page .card .bottom.data-v-53c348c8 {
  padding-top: 24rpx;
  display: flex;
}
.page .card .bottom .left.data-v-53c348c8 {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  padding-top: 10rpx;
}
.page .card .bottom .right.data-v-53c348c8 {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.page .card .bottom .right .tag.data-v-53c348c8 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 44rpx;
  padding: 0 12rpx;
  background: #DCEAFF;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  font-size: 16rpx;
  font-weight: 400;
  color: #2E80FE;
  line-height: 44rpx;
  text-align: center;
  margin: 10rpx;
}
.page .chol .choose.data-v-53c348c8 {
  padding: 40rpx 32rpx;
}
.page .chol .choose .title.data-v-53c348c8 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.page .chol .choose .title ._span.data-v-53c348c8 {
  color: #E72427;
}
.page .chol .choose textarea.data-v-53c348c8 {
  margin-top: 40rpx;
  box-sizing: border-box;
  width: 686rpx;
  height: 242rpx;
  background: #F7F7F7;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  padding: 40rpx 30rpx;
}
.page .chol .choose .desc.data-v-53c348c8 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .chol .choose .up.data-v-53c348c8 {
  margin-bottom: 40rpx;
}
.page .chol .choose .cho_box.data-v-53c348c8 {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.page .chol .choose .cho_box .box_item.data-v-53c348c8 {
  width: -webkit-fit-content;
  width: fit-content;
  padding: 0 20rpx;
  height: 60rpx;
  background: #FFFFFF;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 2rpx solid #D8D8D8;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  line-height: 60rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.page .chol .choose .cho_box .box_item .ok.data-v-53c348c8 {
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: #2E80FE;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .chol .fg.data-v-53c348c8 {
  width: 750rpx;
  height: 20rpx;
  background: #F3F4F5;
}
.page .footer.data-v-53c348c8 {
  height: 192rpx;
  width: 750rpx;
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  position: fixed;
  bottom: 0;
  padding: 0 32rpx;
  display: flex;
  align-items: center;
  background: #FFFFFF;
}
.page .footer .btn.data-v-53c348c8 {
  width: 686rpx;
  height: 88rpx;
  background: #2E80FE;
  border-radius: 44rpx 44rpx 44rpx 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
  text-align: center;
}

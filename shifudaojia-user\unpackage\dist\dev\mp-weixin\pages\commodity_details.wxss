@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-77e7c007 {
  padding-bottom: 166rpx;
}
.page.data-v-77e7c007  .u-popup__content {
  display: none;
}
.page .fg.data-v-77e7c007 {
  height: 20rpx;
  background: #F0F0F0;
}
.page .choose_box.data-v-77e7c007 {
  width: 750rpx;
  height: 692rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  opacity: 1;
  position: fixed;
  bottom: 0;
  z-index: 10076;
  transition: all 0.5s;
}
.page .choose_box .head.data-v-77e7c007 {
  margin-top: 40rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .choose_box .close.data-v-77e7c007 {
  position: absolute;
  top: 44rpx;
  right: 32rpx;
}
.page .choose_box .close image.data-v-77e7c007 {
  width: 37rpx;
  height: 37rpx;
}
.page .choose_box .choose_item.data-v-77e7c007 {
  width: 686rpx;
  height: 200rpx;
  position: relative;
  margin: 0 auto;
  margin-top: 40rpx;
}
.page .choose_box .choose_item image.data-v-77e7c007 {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: -1;
}
.page .choose_box .choose_item .title.data-v-77e7c007 {
  padding-top: 40rpx;
  padding-left: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #824109;
}
.page .choose_box .choose_item .ctx.data-v-77e7c007 {
  max-width: 524rpx;
  margin-top: 16rpx;
  padding-left: 40rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #A38071;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}
.page .header image.data-v-77e7c007 {
  width: 750rpx;
  height: 620rpx;
}
.page .header .Info.data-v-77e7c007 {
  padding: 40rpx 32rpx;
  position: relative;
}
.page .header .Info .title.data-v-77e7c007 {
  max-width: 550rpx;
  font-size: 40rpx;
  font-weight: 500;
  color: #171717;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .header .Info .price.data-v-77e7c007 {
  margin-top: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #E72427;
}
.page .header .Info .num.data-v-77e7c007 {
  margin-top: 12rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .header .Info .share.data-v-77e7c007 {
  position: absolute;
  right: 0;
  top: 96rpx;
  width: 144rpx;
  height: 56rpx;
  background: #F0F0F0;
  border-radius: 28rpx 0rpx 0rpx 28rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}
.page .header .Info .share image.data-v-77e7c007 {
  width: 23rpx;
  height: 21rpx;
  margin-right: 12rpx;
}
.page .header .Info .share text.data-v-77e7c007 {
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .site.data-v-77e7c007 {
  padding: 40rpx 32rpx;
}
.page .site .top.data-v-77e7c007 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .site .top .left.data-v-77e7c007 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .site .top .right.data-v-77e7c007 {
  font-size: 28rpx;
  font-weight: 400;
  color: #2E80FE;
}
.page .site .site_box .site_item.data-v-77e7c007 {
  margin-top: 40rpx;
  height: 208rpx;
  display: flex;
  position: relative;
  border-bottom: 2rpx solid #E9E9E9;
}
.page .site .site_box .site_item image.data-v-77e7c007 {
  width: 202rpx;
  height: 142rpx;
  margin-right: 20rpx;
}
.page .site .site_box .site_item .content.data-v-77e7c007 {
  width: 0;
  flex: 1;
  height: 100%;
}
.page .site .site_box .site_item .content .name.data-v-77e7c007 {
  width: 464rpx;
  min-height: 76rpx;
  font-size: 28rpx;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}
.page .site .site_box .site_item .content .address.data-v-77e7c007 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .site .site_box .site_item .content .address .position.data-v-77e7c007 {
  display: flex;
  align-items: center;
}
.page .site .site_box .site_item .content .address .position text.data-v-77e7c007 {
  font-size: 24rpx;
  color: #333333;
  max-width: 320rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 10rpx;
}
.page .site .site_box .site_item .content .address image.data-v-77e7c007 {
  width: 60rpx;
  height: 60rpx;
  margin: 0;
}
.page .details.data-v-77e7c007 {
  padding: 40rpx 30rpx;
}
.page .details .ddd.data-v-77e7c007 {
  width: 100%;
  height: auto;
}
.page .eva.data-v-77e7c007 {
  padding: 20rpx 30rpx;
}
.page .eva .top.data-v-77e7c007 {
  padding-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2rpx solid #DDDDDD;
}
.page .eva .top .left.data-v-77e7c007 {
  font-size: 28rpx;
  font-weight: 400;
  color: #3B3B3B;
}
.page .eva .top .right.data-v-77e7c007 {
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}
.page .eva .eva_item.data-v-77e7c007 {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #DDDDDD;
}
.page .eva .eva_item .top.data-v-77e7c007 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border: none;
  padding-bottom: 0;
}
.page .eva .eva_item .top image.data-v-77e7c007 {
  width: 46rpx;
  height: 46rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}
.page .eva .eva_item .ctx.data-v-77e7c007 {
  margin-top: 18rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}
.page .footer.data-v-77e7c007 {
  padding: 38rpx 32rpx;
  width: 750rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  opacity: 1;
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
}
.page .footer .lef.data-v-77e7c007 {
  width: 84rpx;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.page .footer .lef image.data-v-77e7c007 {
  width: 36.5rpx;
  height: 36.5rpx;
}
.page .footer .lef text.data-v-77e7c007 {
  margin-top: 10rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #171717;
}
.page .footer .mid.data-v-77e7c007 {
  width: 198rpx;
  height: 90rpx;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  opacity: 1;
  border: 2rpx solid #2E80FE;
  font-size: 28rpx;
  font-weight: 400;
  color: #2E80FE;
  line-height: 90rpx;
  text-align: center;
  margin-left: 26rpx;
  margin-right: 20rpx;
}
.page .footer .righ.data-v-77e7c007 {
  width: 690rpx;
  height: 88rpx;
  background: #2E80FE;
  border-radius: 44rpx 44rpx 44rpx 44rpx;
  opacity: 1;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
  text-align: center;
}

<block wx:if="{{detail.id}}"><view class="order-pages"><view class="item-child pd-lg fill-base f-paragraph c-base" style="{{'background:'+(primaryColor)+';'}}"><view class="flex-y-baseline"><view class="text-bold">{{statusType[detail.pay_type]}}</view><block wx:if="{{detail.pay_type==-1&&detail.coach_refund_time}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="ml-md" catchtap="__e">技师拒单，查看原因</view></block></view><block wx:if="{{detail.pay_type==1&&detail.end_time>0}}"><view class="f-caption mt-sm">请在<min-countdown vue-id="48f551a3-1" targetTime="{{over_time_text}}" data-event-opts="{{[['^callback',[['countEnd']]]]}}" bind:callback="__e" bind:__l="__l"></min-countdown>内完成支付，逾期未支付，订单将自动取消</view></block><view class="space-lg"></view></view><view class="menu-list flex-warp rel ml-lg mr-lg pt-lg pb-lg pl-md pr-md fill-base f-paragraph c-caption radius-16"><view class="menu-line abs b-1px-b"></view><block wx:for="{{lineList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item-child flex-center flex-column f-icontext c-paragraph" style="{{'color:'+(detail.pay_type>item.pay_type-1?primaryColor:'')+';'}}"><view class="item-img fill-base flex-center mb-sm radius" style="{{'border-color:'+(detail.pay_type>item.pay_type-1?primaryColor:'')+';'}}"><view class="{{['iconfont','_i',item.icon]}}"></view></view><view>{{item.title}}</view></view></block></view><view class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16"><view class="flex-between pb-lg"><view class="f-paragraph c-title max-380 ellipsis">预约内容</view></view><block wx:for="{{$root.l0}}" wx:for-item="aitem" wx:for-index="aindex" wx:key="aindex"><view class="{{['flex-center',[(aindex!=aitem.g0-1)?'mb-lg':'']]}}"><image class="avatar lg radius-16" mode="aspectFill" src="{{aitem.$orig.goods_cover}}"></image><view class="flex-1 ml-md"><view class="flex-between"><view class="{{['goods-title','f-title','c-title','ellipsis',[(aitem.$orig.refund_num>0)?'max-300':'']]}}">{{''+aitem.$orig.goods_name+''}}</view><view class="flex-center"><block wx:if="{{aitem.$orig.refund_num>0}}"><view class="f-caption c-warning mr-sm">{{"已退x"+aitem.$orig.refund_num}}</view></block><block wx:if="{{aitem.$orig.is_deposit_mode}}"><view class="time-long2 flex-center">定 金</view></block></view></view><view class="f-caption c-caption">{{"服务技师："+detail.coach_info.coach_name}}</view><view class="f-caption c-caption">{{"预约时间："+detail.start_time}}</view><view class="flex-between"><view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+aitem.$orig.price+''}}</view></view><view class="c-paragraph">{{"x"+aitem.$orig.num}}</view></view></view></view></block></view><view class="mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-caption radius-16"><view class="flex-between"><view>下单人</view><view class="c-title">{{detail.address_info.user_name}}</view></view><view class="flex-between mt-md"><view>联系方式</view><view class="c-title">{{detail.address_info.mobile}}</view></view><view class="mt-md"><view>服务地址</view><view class="c-title mt-sm">{{''+detail.address_info.address+detail.address_info.address_info+''}}</view></view><block wx:if="{{detail.text}}"><view class="mt-md"><view>订单备注</view><view class="c-title mt-sm">{{detail.text}}</view></view></block></view><view class="mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-caption radius-16"><view class="flex-between"><image class="avatar sm radius" src="{{detail.coach_info.work_img}}"></image><view class="c-title">{{detail.coach_info.coach_name}}</view></view><view class="flex-between mt-md"><view>下单时间</view><view class="c-title">{{detail.create_time}}</view></view><view class="flex-between mt-md"><view>服务时间</view><view class="c-title">{{detail.start_time}}</view></view><view class="flex-between mt-md"><view>服务时长</view><view class="c-title">{{detail.time_long+"分钟"}}</view></view><view class="flex-between mt-md"><view>车费详情</view><view class="flex-y-center c-title">{{carType[detail.car_type]+''}}<block wx:if="{{detail.car_type==1}}"><view class="ml-md">{{"全程"+detail.distance}}</view></block></view></view><block wx:if="{{detail.car_type==1}}"><view class="flex-between mt-md"><view>出行费用</view><view class="c-warning">{{"出租车 ¥"+detail.car_price}}</view></view></block><view class="flex-between mt-md"><view>服务项目费用</view><view class="c-warning">{{"¥"+detail.init_service_price}}</view></view><block wx:if="{{detail.discount*1>0}}"><view class="flex-between mt-md"><view>卡券优惠</view><view class="c-warning">{{"-¥"+detail.discount}}</view></view></block><view class="flex-between mt-md"><view>支付方式</view><view class="flex-y-baseline c-title"><view class="{{['iconfont','mr-sm','_i',payType[detail.is_balance].icon]}}" style="{{'color:'+(detail.is_balance==1?primaryColor:'')+';'}}"></view>{{payType[detail.is_balance].text+''}}</view></view><view class="flex-between mt-md pt-md b-1px-t"><view></view><view class="flex-y-baseline c-title">总计：<view class="c-warning">{{"¥"+detail.pay_price}}</view></view></view></view><view class="mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-caption radius-16"><view class="flex-between pb-lg c-title">{{"订单编号："+detail.order_code+''}}</view><timeline vue-id="48f551a3-2" list="{{lineList}}" info="{{detail}}" bind:__l="__l"></timeline></view><view class="space-max-footer"></view><view class="footer-info fix fill-base"><view class="flex-between pd-lg"><view></view><view class="flex-center f-desc c-title"><block wx:if="{{detail.pay_type!=-1}}"><button class="clear-btn item-btn flex-center f-desc" style="margin-right:20rpx;border-radius:100rpx;" open-type="{{configInfo.im_type==2?'contact':''}}" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" catchtap="__e">联系客服</button></block><block wx:if="{{$root.g1}}"><block><block wx:if="{{detail.pay_type==1}}"><block><view data-event-opts="{{[['tap',[['toCancel',['$event']]]]]}}" class="item-btn flex-center radius" catchtap="__e">取消订单</view><view data-event-opts="{{[['tap',[['toPay',['$event']]]]]}}" class="item-btn flex-center ml-md c-base radius" style="{{'background:'+(primaryColor)+';'}}" catchtap="__e">去支付</view></block></block><block wx:if="{{detail.pay_type==-1}}"><block><view data-event-opts="{{[['tap',[['toCancel',['$event']]]]]}}" class="item-btn flex-center radius" catchtap="__e">删除</view></block></block><block wx:if="{{detail.can_refund*1>0}}"><view data-event-opts="{{[['tap',[['goDetail',['refund']]]]]}}" class="item-btn flex-center radius" catchtap="__e">申请退款</view></block><block wx:if="{{detail.pay_type==7}}"><block><block wx:if="{{!detail.is_comment}}"><view data-event-opts="{{[['tap',[['goDetail',['evaluate']]]]]}}" class="item-btn flex-center radius" catchtap="__e">去评价</view></block><view data-event-opts="{{[['tap',[['toAgain',['$event']]]]]}}" class="item-btn flex-center ml-md c-base radius" style="{{'background:'+(primaryColor)+';'}}" catchtap="__e">再来一单</view></block></block></block></block></view></view><view class="space-safe"></view></view><common-popup class="vue-ref" vue-id="48f551a3-3" type="CANCEL_ORDER" info="{{popupInfo}}" data-ref="cancel_item" data-event-opts="{{[['^confirm',[['confirmCancel']]]]}}" bind:confirm="__e" bind:__l="__l"></common-popup><common-popup class="vue-ref" vue-id="48f551a3-4" type="DELETE_ORDER" info="{{popupInfo}}" data-ref="del_item" data-event-opts="{{[['^confirm',[['confirmDel']]]]}}" bind:confirm="__e" bind:__l="__l"></common-popup><uni-popup class="vue-ref" vue-id="48f551a3-5" type="center" custom="{{true}}" data-ref="refuse_item" bind:__l="__l" vue-slots="{{['default']}}"><view class="common-popup-content fill-base pd-lg radius-34"><view class="title">拒单原因</view><scroll-view class="refund-text mt-lg" scroll-y="{{true}}" data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><view><text style="word-break:break-all;" decode="emsp">{{detail.coach_refund_text||'没有填写原因哦'}}</text></view></scroll-view><view class="button"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="item-child c-base" style="{{'background:'+(primaryColor)+';'+('color:'+('#fff')+';')}}" catchtap="__e">知道了</view></view></view></uni-popup></view></block>
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-3ade17cb {
  padding-bottom: 200rpx;
}
.page .header.data-v-3ade17cb {
  width: 750rpx;
  height: 58rpx;
  background: #FFF7F1;
  line-height: 58rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
}
.page .main.data-v-3ade17cb {
  padding: 40rpx 30rpx;
}
.page .main .main_item.data-v-3ade17cb {
  margin-bottom: 20rpx;
}
.page .main .main_item .title.data-v-3ade17cb {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}
.page .main .main_item .title ._span.data-v-3ade17cb {
  color: #E72427;
}
.page .main .main_item input.data-v-3ade17cb {
  width: 690rpx;
  height: 110rpx;
  background: #F8F8F8;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 110rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
}
.page .main .main_item .big.data-v-3ade17cb {
  width: 690rpx;
  height: 388rpx;
  background: #F2FAFE;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}
.page .main .main_item .big .top.data-v-3ade17cb {
  height: 322rpx;
  padding-top: 20rpx;
}
.page .main .main_item .big .top .das.data-v-3ade17cb {
  margin: 0 auto;
  width: 632rpx;
  height: 284rpx;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  border: 2rpx dashed #2E80FE;
  padding-top: 14rpx;
}
.page .main .main_item .big .top .das .up.data-v-3ade17cb {
  margin: 0 auto;
  width: 594rpx;
  height: 258rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 12rpx 12rpx 12rpx 12rpx;
}
.page .main .main_item .big .bottom.data-v-3ade17cb {
  height: 66rpx;
  width: 690rpx;
  height: 66rpx;
  background: #2E80FE;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 66rpx;
  text-align: center;
}
.page .main .main_item .card.data-v-3ade17cb {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page .main .main_item .card .card_item.data-v-3ade17cb {
  width: 332rpx;
  height: 332rpx;
  background: #F2FAFE;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  overflow: hidden;
}
.page .main .main_item .card .card_item .top.data-v-3ade17cb {
  height: 266rpx;
  width: 100%;
  padding-top: 40rpx;
}
.page .main .main_item .card .card_item .top .das.data-v-3ade17cb {
  margin: 0 auto;
  width: 266rpx;
  height: 180rpx;
  border: 2rpx dashed #2E80FE;
  padding-top: 28rpx;
}
.page .main .main_item .card .card_item .top .das .up.data-v-3ade17cb {
  margin: 0 auto;
  width: 210rpx;
  height: 130rpx;
}
.page .main .main_item .card .card_item .bottom.data-v-3ade17cb {
  height: 66rpx;
  width: 100%;
  background-color: #2E80FE;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  text-align: center;
  line-height: 66rpx;
}
.page .footer.data-v-3ade17cb {
  padding: 52rpx 30rpx;
  width: 750rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  position: fixed;
  bottom: 0;
}
.page .footer .btn.data-v-3ade17cb {
  width: 690rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}

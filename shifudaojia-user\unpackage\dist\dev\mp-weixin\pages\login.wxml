<block wx:if="{{isLoad}}"><view class="pages-user-login"><view style="padding:48rpx 52rpx;"><view style="display:flex;align-items:center;color:#999;font-size:32rpx;"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" style="{{'margin-right:60rpx;'+(currentIndex==0?'font-size:48rpx;color: #171717;':'')}}" bindtap="__e">登录</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" style="{{(currentIndex==1?'font-size:48rpx;color: #171717;':'')}}" bindtap="__e">注册</view></view><block wx:if="{{currentIndex==0}}"><view style="margin-top:92rpx;"><u--input bind:input="__e" vue-id="f7872a74-1" prefixIcon="order" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入手机号" border="bottom" value="{{phone}}" data-event-opts="{{[['^input',[['__set_model',['','phone','$event',[]]]]]]}}" bind:__l="__l"></u--input><u--input bind:input="__e" style="margin-top:84rpx;" vue-id="f7872a74-2" prefixIcon="lock" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入密码" border="bottom" value="{{password}}" data-event-opts="{{[['^input',[['__set_model',['','password','$event',[]]]]]]}}" bind:__l="__l"></u--input><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" style="margin-top:10rpx;color:#999;text-align:right;width:100%;text-decoration:underline;" bindtap="__e">忘记密码</view><view data-event-opts="{{[['tap',[['goLogin',['$event']]]]]}}" style="margin-top:50rpx;width:642rpx;height:88rpx;border-radius:12rpx;background-color:#2e80fe;text-align:center;line-height:88rpx;color:#fff;" bindtap="__e">登录</view><view style="display:flex;margin-top:40rpx;font-size:22rpx;"><u-checkbox-group bind:input="__e" vue-id="f7872a74-3" value="{{agree}}" data-event-opts="{{[['^input',[['__set_model',['','agree','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><u-checkbox vue-id="{{('f7872a74-4')+','+('f7872a74-3')}}" label name bind:__l="__l"></u-checkbox></u-checkbox-group><view>我已详细阅读并同意<label data-event-opts="{{[['tap',[['goDetail',[1]]]]]}}" style="{{'color:'+(primaryColor)+';'}}" catchtap="__e" class="_span">《用户隐私协议》</label>与<label data-event-opts="{{[['tap',[['goDetail',[2]]]]]}}" style="{{'color:'+(primaryColor)+';'}}" catchtap="__e" class="_span">《个人信息保护指引》</label></view></view></view></block><block wx:else><block wx:if="{{currentIndex==1}}"><view style="margin-top:92rpx;"><u--input bind:input="__e" vue-id="f7872a74-5" prefixIcon="order" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入手机号" border="bottom" value="{{phone}}" data-event-opts="{{[['^input',[['__set_model',['','phone','$event',[]]]]]]}}" bind:__l="__l"></u--input><u-input bind:input="__e" style="margin-top:84rpx;" vue-id="f7872a74-6" prefixIcon="tags" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入验证码" border="bottom" value="{{short_code}}" data-event-opts="{{[['^input',[['__set_model',['','short_code','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix"><u-code class="vue-ref" vue-id="{{('f7872a74-7')+','+('f7872a74-6')}}" seconds="60" changeText="X秒重新获取" data-ref="uCode" data-event-opts="{{[['^change',[['codeChange']]]]}}" bind:change="__e" bind:__l="__l"></u-code><u-button vue-id="{{('f7872a74-8')+','+('f7872a74-6')}}" text="{{tips}}" type="success" size="mini" data-event-opts="{{[['^tap',[['getCode']]]]}}" bind:tap="__e" bind:__l="__l"></u-button></view></u-input><u--input bind:input="__e" style="margin-top:84rpx;" vue-id="f7872a74-9" prefixIcon="lock" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入密码" border="bottom" value="{{password}}" data-event-opts="{{[['^input',[['__set_model',['','password','$event',[]]]]]]}}" bind:__l="__l"></u--input><view data-event-opts="{{[['tap',[['register',['$event']]]]]}}" style="margin-top:80rpx;width:642rpx;height:88rpx;border-radius:12rpx;background-color:#2e80fe;text-align:center;line-height:88rpx;color:#fff;" bindtap="__e">注册</view></view></block><block wx:else><view style="margin-top:92rpx;"><u-input bind:input="__e" vue-id="f7872a74-10" prefixIcon="order" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入手机号" border="bottom" value="{{phone}}" data-event-opts="{{[['^input',[['__set_model',['','phone','$event',[]]]]]]}}" bind:__l="__l"></u-input><u-input bind:input="__e" style="margin-top:84rpx;" vue-id="f7872a74-11" prefixIcon="tags" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入验证码" border="bottom" value="{{short_code}}" data-event-opts="{{[['^input',[['__set_model',['','short_code','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix"><u-code class="vue-ref" vue-id="{{('f7872a74-12')+','+('f7872a74-11')}}" seconds="60" changeText="X秒重新获取" data-ref="uCode" data-event-opts="{{[['^change',[['codeChange']]]]}}" bind:change="__e" bind:__l="__l"></u-code><u-button vue-id="{{('f7872a74-13')+','+('f7872a74-11')}}" text="{{tips}}" type="success" size="mini" data-event-opts="{{[['^tap',[['getCode']]]]}}" bind:tap="__e" bind:__l="__l"></u-button></view></u-input><u-input bind:input="__e" style="margin-top:84rpx;" vue-id="f7872a74-14" prefixIcon="lock" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入密码" border="bottom" value="{{password}}" data-event-opts="{{[['^input',[['__set_model',['','password','$event',[]]]]]]}}" bind:__l="__l"></u-input><u-input bind:input="__e" style="margin-top:84rpx;" vue-id="f7872a74-15" prefixIcon="lock" prefixIconStyle="font-size: 30px;color: #999999" placeholder="确认密码" border="bottom" value="{{againpassword}}" data-event-opts="{{[['^input',[['__set_model',['','againpassword','$event',[]]]]]]}}" bind:__l="__l"></u-input><view data-event-opts="{{[['tap',[['forget',['$event']]]]]}}" style="margin-top:80rpx;width:642rpx;height:88rpx;border-radius:12rpx;background-color:#2e80fe;text-align:center;line-height:88rpx;color:#fff;" bindtap="__e">重置密码</view></view></block></block></view><block wx:if="{{currentIndex==0}}"><view class="flex-center flex-column"><image class="logo-img mb-md" mode="aspectFill" lazy-load="{{true}}" src="{{base_info.app_logo}}"></image><view class="f-caption c-caption">{{base_info.app_text}}</view><block wx:if="{{SystemInfo=='ios'}}"><view data-event-opts="{{[['tap',[['appleLogin',['$event']]]]]}}" bindtap="__e"><view class="appleBtn"><image src="../static/images/apple-fill.png" mode></image><view>通过Apple登录</view></view></view></block><view data-event-opts="{{[['tap',[['startWxLogin',['$event']]]]]}}" class="wechat-login flex-center f-title c-base" style="{{'background:'+('linear-gradient(90deg, '+primaryColor+' 0%, '+subColor+' 99%)')+';'}}" bindtap="__e"><view class="iconfont iconweixin mr-sm _i"></view>微信登录</view></view></block></view></block>
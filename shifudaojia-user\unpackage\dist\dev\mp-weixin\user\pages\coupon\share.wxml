<block wx:if="{{isLoad}}"><view class="user-coupon-share"><image class="coupon-img" src="{{shareImg}}"></image><view class="coupon-content"><view class="flex-center flex-column"><view class="flex-center f-paragraph pt-lg pb-md" style="{{'color:'+(primaryColor)+';'}}">{{''+(detail.status==1?'距活动结束还剩':'活动已结束')+''}}</view><view class="count-down"><countdown vue-id="072dc99f-1" type="2" targetTime="{{over_time_text}}" data-event-opts="{{[['^callback',[['countEnd']]]]}}" bind:callback="__e" bind:__l="__l"></countdown></view><view class="space-lg"></view><view class="space-lg"></view><view class="ml-lg mr-lg pt-lg pb-sm pl-lg pr-lg rel radius-20" style="background:#FFFAF4;"><view class="menu-img mt-lg abs"><image class="menu-img" src="/static/coupon/menu.png"></image><view class="menu-title flex-center abs" style="{{'color:'+(primaryColor)+';'}}">邀请记录</view></view><view class="space-lg"></view><view class="space-lg"></view><view class="flex-center f-desc pb-md" style="{{'color:'+(primaryColor)+';'}}"><view class="user-item">用户</view><view class="time-item">时间</view></view><image class="line-img" src="/static/coupon/line.png"></image><block wx:if="{{$root.g0>0}}"><scroll-view class="user-list-info" scroll-y="{{true}}"><block wx:for="{{record_list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="user-list flex-between f-desc c-title b-1px-b"><view class="flex-y-center pt-md pb-md"><image class="avatar radius" src="{{item.avatarUrl}}"></image><view class="ml-md max-300 ellipsis">{{item.nickName||'用户'+item.id}}</view></view><view>{{item.create_time}}</view></view></block></scroll-view></block><block wx:if="{{$root.g1==0}}"><abnor vue-id="072dc99f-2" percent="70%" bind:__l="__l"></abnor></block><view class="space-sm"></view><block wx:if="{{detail.status==1}}"><auth vue-id="072dc99f-3" needAuth="{{userInfo&&(!userInfo.phone||!userInfo.nickName)}}" must="{{true}}" type="{{!userInfo.phone?'phone':'userInfo'}}" data-event-opts="{{[['^go',[['toShare']]]]}}" bind:go="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="share-img mt-lg rel"><image class="share-img" src="/static/coupon/btn.png"></image><view class="share-title flex-center abs" style="{{'color:'+(primaryColor)+';'}}">{{''+(detail.user_id==userInfo.id?'邀请有奖':userInfo.nickName?'帮TA邀请':'我要参加')+''}}</view></view></auth></block><block wx:else><view class="share-img grayscale mt-lg rel"><image class="share-img" src="/static/coupon/btn.png"></image><view class="share-title flex-center abs" style="{{'color:'+(primaryColor)+';'}}">{{''+(detail.user_id==userInfo.id?'邀请有奖':userInfo.nickName?'帮TA邀请':'我要参加')+''}}</view></view></block></view><block wx:if="{{detail.id}}"><view class="mt-md ml-lg mr-lg pt-lg pb-lg pl-lg pr-lg rel radius-20" style="background:#FFFAF4;"><view class="atv-rule pd-md radius-16" style="{{'border:'+('1px solid '+primaryColor)+';'}}"><view class="flex-center f-paragraph pt-sm pb-md" style="{{'color:'+(primaryColor)+';'}}">活动规则</view><view class="f-desc c-desc">{{"1、活动总计可发起"+detail.atv_num+"次；"}}</view><block wx:if="{{detail.to_inv_user==1}}"><view class="f-desc c-desc">2、被推荐人授权用户信息后即可获得相应奖励；</view></block><view class="f-desc c-desc">{{''+(detail.to_inv_user==1?3:2)+"、活动发起人每成功邀请"+detail.inv_user_num+'位好友授权用户信息后，即可获得以下卡券：'}}</view><block wx:for="{{detail.coupon}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="flex-warp f-desc c-desc"><view>{{"（"+(index*1+1)+"）卡券："+item.title+"；数量：x"+item.num}}</view></view></block><view class="space-md"></view></view></view></block><view class="space-footer"></view></view></view><uni-popup class="vue-ref" vue-id="072dc99f-4" type="bottom" data-ref="show_share_item" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-share pd-lg f-desc c-desc fill-base"><view class="flex-center"><button class="clear-btn list-item flex-center flex-column" open-type="share"><image class="item-image" src="/static/coupon/wechat.png"></image><view style="font-size:26rpx;color:#666;">分享给好友</view></button><view data-event-opts="{{[['tap',[['toPoster',['$event']]]]]}}" class="list-item flex-center flex-column" catchtap="__e"><image class="item-image" src="/static/coupon/picture.png"></image><view>生成海报码</view></view></view><view class="space-footer"></view></view></uni-popup></view></block>
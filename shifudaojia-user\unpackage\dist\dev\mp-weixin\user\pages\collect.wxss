@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pages-technician .list-item .top-tag {
  width: 40rpx;
  height: 30rpx;
  color: #B75E1D;
  background: linear-gradient(90deg, #DFB885 0%, #FCE0AD 100%);
  border-radius: 8rpx 0 8rpx 0;
  top: 0;
  left: 0;
}
.pages-technician .list-item .item-img {
  width: 124rpx;
  height: 124rpx;
}
.pages-technician .list-item .top-img {
  width: 146rpx;
  height: 140rpx;
  top: -7rpx;
  left: -11rpx;
}
.pages-technician .list-item .hot-img {
  width: 75rpx;
  height: 51rpx;
  top: 77rpx;
  left: 25rpx;
}
.pages-technician .list-item .new-img {
  width: 38rpx;
  height: 52rpx;
  top: 83rpx;
  left: 78rpx;
}
.pages-technician .list-item .item-tag {
  width: 100rpx;
  height: 32rpx;
  color: #000;
  background: rgba(216, 216, 216, 0.3);
  margin-top: 19rpx;
  margin-bottom: 6rpx;
}
.pages-technician .list-item .item-tag.can-service {
  color: #EBDDB1;
  background: linear-gradient(270deg, #4C545A 0%, #282B34 100%);
}
.pages-technician .list-item .more-img {
  width: 104rpx;
  height: 33rpx;
  border-radius: 3px;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
}
.pages-technician .list-item .can-service-btn {
  width: 64rpx;
  height: 28rpx;
  margin-left: 8rpx;
}
.pages-technician .list-item .can-service-btn .bg {
  width: 64rpx;
  height: 28rpx;
  opacity: 0.1;
  border-radius: 3rpx;
  top: 0;
  left: 0;
  z-index: 1;
}
.pages-technician .list-item .can-service-btn .text {
  width: 64rpx;
  height: 28rpx;
  top: 0;
  left: 0;
  z-index: 2;
}
.pages-technician .list-item .iconyduixingxingshixin {
  font-size: 28rpx;
  background-image: -webkit-linear-gradient(270deg, #FAD961 0%, #F76B1C 100%);
}
.pages-technician .list-item .star-text {
  color: #FF9519;
  margin-left: 6rpx;
}
.pages-technician .list-item .order-num {
  color: #4D4D4D;
  margin-left: 16rpx;
}
.pages-technician .list-item .item-btn {
  width: 130rpx;
  height: 52rpx;
  border-radius: 8rpx;
}
.pages-technician .technician-popup {
  border-radius: 20rpx 20rpx 0 0;
}
.pages-technician .technician-popup .item-avatar {
  width: 88rpx;
  height: 88rpx;
  background: #f4f6f8;
}
.pages-technician .technician-popup .icon-close {
  font-size: 50rpx;
}
.pages-technician .technician-popup .technician-text {
  max-height: 150rpx;
}
.pages-technician .technician-popup .list-content {
  max-height: 60vh;
}
.pages-technician .technician-popup .list-content .list-message .item-avatar {
  width: 52rpx;
  height: 52rpx;
  background: #f4f6f8;
}
.pages-technician .technician-popup .list-content .list-message .iconyduixingxingshixin {
  font-size: 28rpx;
  margin-right: 5rpx;
  font-size: 28rpx;
}
.pages-technician .technician-popup .order-btn {
  width: 200rpx;
  height: 72rpx;
}

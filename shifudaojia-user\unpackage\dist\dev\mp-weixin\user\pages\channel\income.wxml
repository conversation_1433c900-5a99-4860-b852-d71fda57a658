<block wx:if="{{isLoad}}"><view class="user-channel-index"><view class="mine-menu-list c-base" style="{{'background:'+(primaryColor)+';'}}"><view class="flex-between pt-md pl-lg pb-lg"><view class="flex-center"><view class="f-desc mr-sm">渠道分销商</view><view class="f-title">{{detail.cate_text}}</view></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="channel-code flex-center f-icontext" catchtap="__e"><view class="iconfont iconerweima _i"></view>渠道码<view class="iconfont icon-down _i"></view></view></view><view class="money-count flex-center pb-md"><view class="item-child flex-center flex-column"><view class="num text-bold">{{detail.order_price}}</view><view class="f-caption">订单总金额</view></view><view class="line"></view><view class="item-child flex-center flex-column"><view class="num text-bold">{{detail.order_count}}</view><view class="f-caption">订单数量</view></view></view></view><view class="search-info flex-between fill-base"><view class="item-search"><search vue-id="3f28bfc0-1" type="input" placeholder="请输入服务名称/订单号" data-event-opts="{{[['^input',[['toSearch']]],['^confirm',[['toSearch']]]]}}" bind:input="__e" bind:confirm="__e" bind:__l="__l"></search></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="flex-center pr-md f-paragraph c-title" bindtap="__e">筛选<view class="iconfont iconshaixuanxia-1 _i"></view></view></view><view class="order-pages"><block wx:for="{{list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16"><view class="flex-between pb-lg"><view class="f-paragraph c-title max-350 ellipsis">{{"下单人："+item.address_info.user_name}}</view><view class="f-caption c-caption">{{item.create_time}}</view></view><block wx:for="{{item.order_goods}}" wx:for-item="aitem" wx:for-index="aindex" wx:key="aindex"><view class="flex-center mb-lg"><image class="avatar lg radius-16" mode="aspectFill" src="{{aitem.goods_cover}}"></image><view class="flex-1 ml-md"><view class="flex-between"><view class="{{['goods-title','f-title','c-title','ellipsis',[(aitem.refund_num>0)?'max-300':'']]}}">{{''+aitem.goods_name+''}}</view><block wx:if="{{aitem.refund_num>0}}"><view class="f-caption c-warning">{{"已退x"+aitem.refund_num}}</view></block></view><view class="f-caption c-caption mt-sm mb-sm">{{"服务技师："+(item.coach_info.coach_name||'')}}</view><view class="flex-between"><view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+aitem.true_price+''}}</view></view><view class="c-paragraph">{{"x"+aitem.num}}</view></view></view></view></block><view class="flex-between"><view><block wx:if="{{item.refund_price*1>0}}"><view class="flex-y-center f-caption">总计退款：<view class="c-warning">{{"¥"+item.refund_price}}</view></view></block></view><view class="flex-y-center f-desc c-title">付款：<view class="text-bold">{{"¥"+item.true_service_price}}</view></view></view></view></block></view><block wx:if="{{loading}}"><load-more vue-id="3f28bfc0-2" noMore="{{$root.g0}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g1}}"><abnor vue-id="3f28bfc0-3" bind:__l="__l"></abnor></block><view class="space-footer"></view><uni-popup class="vue-ref" vue-id="3f28bfc0-4" type="bottom" maskClick="{{false}}" data-ref="rank_item" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-rank fill-base"><view class="flex-between pd-lg"><view class="f-title c-title text-bold">选择筛选条件</view><view data-event-opts="{{[['tap',[['toConfirm',[1]]]]]}}" class="f-caption c-caption" bindtap="__e">取消</view></view><view class="pd-lg"><view class="pt-lg pb-lg f-paragraph text-bold">时间</view><view class="flex-warp pb-lg"><block wx:for="{{tabList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toChangeItem',[index,'tabInd']]]]]}}" class="{{['item-rank','flex-center','f-desc','c-title','radius-16',[(index!=0)?'ml-md':'']]}}" style="{{'background:'+(index==tabInd?primaryColor:'')+';'+('color:'+(index==tabInd?'#fff':'')+';')+('border-color:'+(index==tabInd?primaryColor:'')+';')}}" bindtap="__e">{{item.title}}</view></block></view><block wx:if="{{tabInd==3}}"><view class="flex-between pt-lg pb-lg"><view class="f-paragraph text-bold">开始时间</view><picker mode="date" start="1900-01-01" end="{{today}}" data-event-opts="{{[['change',[['pickerChange',['$event','start_time']]]]]}}" bindchange="__e"><view class="{{['flex-y-center','f-title',[(tabList[3].start_time)?'c-title':''],[(!tabList[3].start_time)?'f-caption c-caption':'']]}}">{{''+(tabList[3].start_time||'请选择')+''}}<view class="iconfont icon-right c-caption _i"></view></view></picker></view></block><block wx:if="{{tabInd==3}}"><view class="flex-between pt-md pb-lg"><view class="f-paragraph text-bold">结束时间</view><picker mode="date" start="1900-01-01" end="{{today}}" data-event-opts="{{[['change',[['pickerChange',['$event','end_time']]]]]}}" bindchange="__e"><view class="{{['flex-y-center','f-title',[(tabList[3].end_time)?'c-title':''],[(!tabList[3].end_time)?'f-caption c-caption':'']]}}">{{''+(tabList[3].end_time||'请选择')+''}}<view class="iconfont icon-right c-caption _i"></view></view></picker></view></block></view><view class="btn-info flex-center pd-lg"><view data-event-opts="{{[['tap',[['toConfirm',[2]]]]]}}" class="item-child flex-center fill-base f-desc radius" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['toConfirm',[3]]]]]}}" class="item-child flex-center f-desc c-base radius" style="{{'background:'+(primaryColor)+';'+('border-color:'+(primaryColor)+';')}}" bindtap="__e">查询</view></view><view class="space-safe"></view></view></uni-popup></view></block>
<block wx:if="{{detail.id}}"><view class="technician-info"><view style="{{'background:'+(primaryColor)+';'+('height:'+('196rpx')+';')}}"></view><view class="fill-base ml-lg mr-lg flex-center pd-lg radius-16 rel" style="margin-top:-100rpx;"><image class="avatar radius abs" mode="aspectFill" src="{{detail.work_img}}" data-event-opts="{{[['tap',[['toPreviewImage',['$0','work_img'],['index']]]]]}}" catchtap="__e"></image><view class="flex-1 ml-lg" style="margin-left:180rpx;"><view class="flex-y-baseline f-caption c-title"><view class="f-title text-bold mr-lg">{{detail.coach_name}}</view><view class="sex-tag flex-center f-icontext radius"><view class="{{['iconfont','_i',['iconnan-xiaotu','iconnv-xiaotu'][detail.sex]]}}" style="{{'color:'+(detail.sex===1?'#ee6faa':'#1296db')+';'}}"></view>{{''+['男','女'][detail.sex]+''}}</view></view><view class="f-paragraph c-paragraph" style="margin-top:5rpx;">{{'从业'+detail.work_time+'年'}}</view></view></view><view class="ml-lg mr-lg"><view class="fill-base mt-md pd-lg f-paragraph c-paragraph radius-16"><view class="f-title c-title text-bold mb-md">基本信息</view><view class="flex-y-baseline f-paragraph c-caption">意向工作城市<view class="c-paragraph ml-lg">{{''+detail.city+''}}</view></view><view class="c-caption text-bold mt-md mb-md">技师简介</view><text class="c-paragraph" style="word-break:break-all;" decode="emsp">{{detail.text}}</text></view><view class="fill-base mt-md pd-lg f-paragraph c-paragraph radius-16"><view class="f-title c-title text-bold">资质证书</view><view class="flex-warp img-list"><block wx:for="{{detail.license}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image class="img-item-mini radius-16" mode="aspectFill" src="{{item}}" data-event-opts="{{[['tap',[['toPreviewImage',[index,'license']]]]]}}" catchtap="__e"></image></block></view></view><view class="fill-base mt-md pd-lg f-paragraph c-paragraph radius-16"><view class="f-title c-title text-bold">生活照</view><view class="flex-warp img-list"><block wx:for="{{detail.self_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image class="img-item-mini radius-16" mode="aspectFill" src="{{item}}" data-event-opts="{{[['tap',[['toPreviewImage',[index,'self_img']]]]]}}" catchtap="__e"></image></block></view></view><block wx:if="{{detail.video}}"><view class="fill-base mt-md pd-lg f-paragraph c-paragraph radius-16"><view class="f-title c-title text-bold">视频介绍</view><video class="item-video mt-md radius-16" style="overflow:hidden;" loop="{{false}}" enable-play-gesture="{{true}}" enable-progress-gesture="{{true}}" src="{{detail.video}}" data-event-opts="{{[['waiting',[['onWaiting',['$event']]]],['progress',[['onProgress',['$event']]]],['loadedmetadata',[['onLoadedMetaData',['$event']]]]]}}" bindwaiting="__e" bindprogress="__e" bindloadedmetadata="__e"></video></view></block></view><view class="space-footer"></view></view></block>
<view class="mine-pages-setting"><u-modal vue-id="3549ecd2-1" show="{{show1}}" title="修改密码" showCancelButton="{{true}}" data-event-opts="{{[['^confirm',[['confirmmm']]],['^cancel',[['cancelmm']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:__l="__l" vue-slots="{{['default']}}"><view><view style="margin-bottom:10rpx;"><u--input bind:input="__e" vue-id="{{('3549ecd2-2')+','+('3549ecd2-1')}}" prefixIcon="lock" placeholder="输入密码" type="password" value="{{newpassword}}" data-event-opts="{{[['^input',[['__set_model',['','newpassword','$event',[]]]]]]}}" bind:__l="__l"></u--input></view><view style="margin-bottom:10rpx;"><u--input bind:input="__e" vue-id="{{('3549ecd2-3')+','+('3549ecd2-1')}}" prefixIcon="lock" placeholder="再次输入密码" type="password" value="{{againpassword}}" data-event-opts="{{[['^input',[['__set_model',['','againpassword','$event',[]]]]]]}}" bind:__l="__l"></u--input></view><view style="margin-bottom:10rpx;"><u-input bind:input="__e" style="margin-top:84rpx;" vue-id="{{('3549ecd2-4')+','+('3549ecd2-1')}}" prefixIcon="tags" prefixIconStyle="font-size: 30px;color: #999999" placeholder="请输入验证码" border="bottom" value="{{yzmcode}}" data-event-opts="{{[['^input',[['__set_model',['','yzmcode','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix"><u-code class="vue-ref" vue-id="{{('3549ecd2-5')+','+('3549ecd2-4')}}" seconds="60" changeText="X秒重新获取" data-ref="uCode" data-event-opts="{{[['^change',[['codeChange']]]]}}" bind:change="__e" bind:__l="__l"></u-code><u-button vue-id="{{('3549ecd2-6')+','+('3549ecd2-4')}}" text="{{tips}}" type="success" size="mini" data-event-opts="{{[['^tap',[['getCode']]]]}}" bind:tap="__e" bind:__l="__l"></u-button></view></u-input></view></view></u-modal><u-modal vue-id="3549ecd2-7" show="{{show}}" title="注销账号" content="确定要注销本账号吗(注销后账号无法恢复,个人数据将被清空)" showCancelButton="{{true}}" data-event-opts="{{[['^confirm',[['confirm']]],['^cancel',[['cancel']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:__l="__l"></u-modal><view class="flex-center flex-column pd-lg fill-base"><view class="space-lg"></view><view class="space-lg"></view><upload vue-id="3549ecd2-8" imagelist="{{user_info.avatarUrl}}" imgtype="avatarUrl" imgsize="{{1}}" radius="{{true}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^changeShow',[['changeShow']]]]}}" bind:upload="__e" bind:changeShow="__e" bind:__l="__l"></upload></view><view class="flex-between pd-lg fill-base f-paragraph"><view>昵称</view><input style="text-align:right;color:#ADADAD;max-width:400rpx;" type="nickname" data-event-opts="{{[['blur',[['bindblur',['$event']]]],['input',[['__set_model',['$0','nickName','$event',[]],['user_info']],['bindinput',['$event']]]]]}}" value="{{user_info.nickName}}" bindblur="__e" bindinput="__e"/></view><view class="flex-between pd-lg fill-base f-paragraph"><view>注册时间</view><input style="text-align:right;color:#ADADAD;max-width:400rpx;" type="text" disabled="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','create_date','$event',[]],['user_info']]]]]}}" value="{{user_info.create_date}}" bindinput="__e"/></view><view class="flex-between pd-lg fill-base f-paragraph"><view>手机号</view><button style="color:#ADADAD;" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['authPhone',['$event']]]]]}}" bindgetphonenumber="__e">{{''+user_info.phone+''}}</button></view><view data-event-opts="{{[['tap',[['editpassword',['$event']]]]]}}" class="flex-between pd-lg fill-base f-paragraph" bindtap="__e"><view>修改密码</view></view><block wx:for="{{infoList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',[index,'infoList']]]]]}}" class="flex-between pd-lg fill-base f-paragraph" catchtap="__e"><view>{{item.text}}</view><view class="iconfont icon-right _i"></view></view></block><view style="display:flex;justify-content:space-around;align-items:center;margin-top:10rpx;"><button data-event-opts="{{[['tap',[['save',['$event']]]]]}}" style="background-color:#2367c7;width:200rpx;border-radius:20rpx;height:80rpx;line-height:80rpx;" bindtap="__e">保存</button></view></view>
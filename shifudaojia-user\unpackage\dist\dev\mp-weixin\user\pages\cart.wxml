<view class="pages-technician"><block wx:for="{{carList.list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['list-item','flex-center','pd-lg','fill-base','radius-16',[(index!=0)?'b-1px-t':'']]}}"><image class="avatar lg radius-16" mode="aspectFill" src="{{item.cover}}"></image><view class="flex-1 ml-md"><view class="f-title c-title max-510 ellipsis">{{item.title}}</view><view class="f-caption c-caption mt-sm mb-sm ellipsis">{{item.total_sale+"人选择"}}</view><view class="flex-between"><view class="flex-y-center f-desc c-caption max-350 ellipsis"><view class="f-title c-warning mr-sm">{{"¥"+item.price}}</view>{{"/ "+item.time_long+'分钟'}}</view><view class="flex-warp"><block wx:if="{{item.num}}"><block><button data-event-opts="{{[['tap',[['changeNum',[-1,index]]]]]}}" class="reduce" style="{{'border-color:'+(primaryColor)+';'+('color:'+(primaryColor)+';')}}" catchtap="__e"><view class="iconfont icon-jian-bold _i"></view></button><button class="addreduce clear-btn">{{item.num||0}}</button></block></block><button data-event-opts="{{[['tap',[['changeNum',[1,index]]]]]}}" class="add" style="{{'background:'+(primaryColor)+';'+('border-color:'+(primaryColor)+';')}}" catchtap="__e"><view class="iconfont icon-jia-bold _i"></view></button></view></view></view></view></block><block wx:if="{{$root.g0}}"><abnor vue-id="637a1075-1" bind:__l="__l"></abnor></block><view class="space-max-footer"></view><view class="fix fill-base pd-lg b-1px-t" style="bottom:0;"><view class="flex-between"><view class="flex-center">合计：<view class="f-title c-warning text-bold ml-sm">{{"¥"+carList.car_price+''}}</view></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="order-btn flex-center f-desc c-base radius" style="{{'background:'+(primaryColor)+';'}}" catchtap="__e">提交订单</view></view><view class="space-safe"></view></view></view>
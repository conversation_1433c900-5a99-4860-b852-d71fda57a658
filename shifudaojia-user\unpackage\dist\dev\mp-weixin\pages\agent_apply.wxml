<view class="page data-v-3ade17cb"><u-picker vue-id="20e5629b-1" show="{{show}}" columns="{{columns}}" keyName="title" data-event-opts="{{[['^cancel',[['e0']]],['^confirm',[['confirmType']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-3ade17cb" bind:__l="__l"></u-picker><u-picker vue-id="20e5629b-2" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeHandler']]],['^cancel',[['e1']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-3ade17cb vue-ref" bind:__l="__l"></u-picker><block wx:if="{{status!==''}}"><view class="header data-v-3ade17cb" style="{{('color:'+arr[status].color)}}">{{arr[status].text}}</view></block><view class="main data-v-3ade17cb"><view class="main_item data-v-3ade17cb"><view class="title data-v-3ade17cb"><label class="_span data-v-3ade17cb">*</label>法人姓名</view><input type="text" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','legal_person_name','$event',[]],['form']]]]]}}" value="{{form.legal_person_name}}" bindinput="__e" class="data-v-3ade17cb"/></view><view class="main_item data-v-3ade17cb"><view class="title data-v-3ade17cb"><label class="_span data-v-3ade17cb">*</label>法人身份证号</view><input type="text" placeholder="请输入身份证号" data-event-opts="{{[['input',[['__set_model',['$0','legal_person_idcard','$event',[]],['form']]]]]}}" value="{{form.legal_person_idcard}}" bindinput="__e" class="data-v-3ade17cb"/></view><view class="main_item data-v-3ade17cb"><view class="title data-v-3ade17cb"><label class="_span data-v-3ade17cb">*</label>联系电话</view><input type="text" placeholder="请输入联系电话" data-event-opts="{{[['input',[['__set_model',['$0','legal_person_tel','$event',[]],['form']]]]]}}" value="{{form.legal_person_tel}}" bindinput="__e" class="data-v-3ade17cb"/></view><view class="main_item data-v-3ade17cb"><view class="title data-v-3ade17cb"><label class="_span data-v-3ade17cb">*</label>选择省市区代理</view><input type="text" placeholder="请选择代理级别" disabled="{{true}}" data-event-opts="{{[['tap',[['e2',['$event']]]],['input',[['__set_model',['$0','typename','$event',[]],['form']]]]]}}" value="{{form.typename}}" bindtap="__e" bindinput="__e" class="data-v-3ade17cb"/></view><view class="main_item data-v-3ade17cb"><view class="title data-v-3ade17cb"><label class="_span data-v-3ade17cb">*</label>选择区域</view><input type="text" placeholder="请选择代理区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e3',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e" class="data-v-3ade17cb"/></view><view class="main_item data-v-3ade17cb"><view class="title data-v-3ade17cb"><label class="_span data-v-3ade17cb">*</label>上传法人身份证照片</view><view class="card data-v-3ade17cb"><view class="card_item data-v-3ade17cb"><view class="top data-v-3ade17cb"><view class="das data-v-3ade17cb"><view class="up data-v-3ade17cb"><upload vue-id="20e5629b-3" imagelist="{{form.legal_person_idcard_img1}}" imgtype="legal_person_idcard_img1" imgclass="id_card_box" text="身份证人像面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^changeShow',[['changeShow']]]]}}" bind:upload="__e" bind:changeShow="__e" class="data-v-3ade17cb" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-3ade17cb">拍摄人像面</view></view><view class="card_item data-v-3ade17cb"><view class="top data-v-3ade17cb"><view class="das data-v-3ade17cb"><view class="up data-v-3ade17cb"><upload vue-id="20e5629b-4" imagelist="{{form.legal_person_idcard_img2}}" imgtype="legal_person_idcard_img2" imgclass="id_card_box" text="身份证国徽面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^changeShow',[['changeShow']]]]}}" bind:upload="__e" bind:changeShow="__e" class="data-v-3ade17cb" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-3ade17cb">拍摄国徽面</view></view></view></view><view class="main_item data-v-3ade17cb"><view class="title data-v-3ade17cb"><label class="_span data-v-3ade17cb">*</label>上传营业执照照片</view><view class="big data-v-3ade17cb"><view class="top data-v-3ade17cb"><view class="das data-v-3ade17cb"><view class="up data-v-3ade17cb"><upload vue-id="20e5629b-5" imagelist="{{form.legal_person_license}}" imgtype="legal_person_license" imgclass="id_yy_box" text="营业执照" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^changeShow',[['changeShow']]]]}}" bind:upload="__e" bind:changeShow="__e" class="data-v-3ade17cb" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-3ade17cb">拍摄营业执照</view></view></view></view><view class="footer data-v-3ade17cb"><block wx:if="{{status!==1&&status!==0}}"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="btn data-v-3ade17cb" bindtap="__e">立即提交</view></block></view></view>
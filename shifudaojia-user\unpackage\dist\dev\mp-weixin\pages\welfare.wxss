@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-4f323623 {
  overflow: auto;
  background-color: #F8F8F8;
  height: 100vh;
  padding-top: 40rpx;
}
.page .main_item.data-v-4f323623 {
  margin: 0 auto;
  width: 690rpx;
  height: 202rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin-bottom: 20rpx;
}
.page .main_item .top.data-v-4f323623 {
  height: 150rpx;
  display: flex;
  align-items: center;
  padding-top: 26rpx;
  padding-left: 24rpx;
  padding-right: 14rpx;
  position: relative;
  border-bottom: 2rpx solid #E9E9E9;
}
.page .main_item .top .box1.data-v-4f323623 {
  text-align: center;
  width: 180rpx;
  font-size: 40rpx;
  font-weight: 500;
  color: #E72427;
}
.page .main_item .top .box1 ._span.data-v-4f323623 {
  font-size: 20rpx;
}
.page .main_item .top .box2.data-v-4f323623 {
  margin-left: 28rpx;
}
.page .main_item .top .box2 text.data-v-4f323623 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main_item .top .box2 ._span.data-v-4f323623 {
  margin-top: 10rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #B2B2B2;
}
.page .main_item .top .box3.data-v-4f323623 {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
  width: 100rpx;
  height: 42rpx;
  background: #2E80FE;
  border-radius: 22rpx 22rpx 22rpx 22rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 42rpx;
  text-align: center;
}
.page .main_item .bottom.data-v-4f323623 {
  padding: 0 24rpx;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 50rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #B2B2B2;
}

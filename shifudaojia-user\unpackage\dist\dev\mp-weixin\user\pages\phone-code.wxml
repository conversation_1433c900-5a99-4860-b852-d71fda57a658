<view class="mine-pages-phone flex-center flex-column"><view class="title c-black">请输入验证码</view><view class="flex-y-center f-caption c-caption mt-lg">验证码已发送至<view class="ml-sm" style="{{'color:'+(primaryColor)+';'}}">{{''+subForm.phone+''}}</view></view><view class="space-lg"></view><view class="space-lg"></view><view class="space-md"></view><view><xt-verify-code bind:confirm="__e" bind:input="__e" vue-id="7b446a2e-1" value="{{subForm.short_code}}" data-event-opts="{{[['^confirm',[['confirm']]],['^input',[['__set_model',['$0','short_code','$event',[]],['subForm']]]]]}}" bind:__l="__l"></xt-verify-code></view><view class="flex-center f-caption c-caption mt-lg"><block wx:if="{{authTime>0}}"><view class="flex-center"><view class="c-title mr-sm">{{authTime+'s'}}</view>后</view></block><view data-event-opts="{{[['tap',[['toResetCode',['$event']]]]]}}" style="{{'color:'+(authTime>0?'':primaryColor)+';'}}" bindtap="__e">重新获取</view></view><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="confirm-btn flex-center f-sm-title text-bold c-base radius-16" style="{{'background:'+($root.g0>0?primaryColor:'#CCE9DD')+';'}}" bindtap="__e">确定</view></view>
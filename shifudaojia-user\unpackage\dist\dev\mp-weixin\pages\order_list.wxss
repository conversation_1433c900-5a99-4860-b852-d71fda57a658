@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-cb9d26b0 {
  background-color: #F8F8F8;
  min-height: 100vh;
  padding-top: 100rpx;
}
.page .header.data-v-cb9d26b0 {
  position: fixed;
  top: 0;
  width: 750rpx;
  height: 100rpx;
  background: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.page .header .header_item.data-v-cb9d26b0 {
  max-width: 85rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.page .header .header_item .blue.data-v-cb9d26b0 {
  margin-top: 8rpx;
  width: 38rpx;
  height: 6rpx;
  background: #2E80FE;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
}
.page .main.data-v-cb9d26b0 {
  padding: 40rpx 30rpx;
}
.page .main .main_item.data-v-cb9d26b0 {
  width: 690rpx;
  height: 284rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 28rpx 36rpx;
  margin-bottom: 20rpx;
}
.page .main .main_item .head.data-v-cb9d26b0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item .head .no.data-v-cb9d26b0 {
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid.data-v-cb9d26b0 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .mid .lef.data-v-cb9d26b0 {
  display: flex;
  align-items: center;
}
.page .main .main_item .mid .lef image.data-v-cb9d26b0 {
  width: 120rpx;
  height: 120rpx;
}
.page .main .main_item .mid .lef text.data-v-cb9d26b0 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid .righ.data-v-cb9d26b0 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
}
.page .main .main_item .bot.data-v-cb9d26b0 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .bot .qzf.data-v-cb9d26b0,
.page .main .main_item .bot .qxdd.data-v-cb9d26b0,
.page .main .main_item .bot .lxsf.data-v-cb9d26b0,
.page .main .main_item .bot .qrwc.data-v-cb9d26b0,
.page .main .main_item .bot .qpl.data-v-cb9d26b0 {
  width: 148rpx;
  height: 48rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 48rpx;
  text-align: center;
}
.page .main .main_item .bot .qzf.data-v-cb9d26b0,
.page .main .main_item .bot .qrwc.data-v-cb9d26b0,
.page .main .main_item .bot .qpl.data-v-cb9d26b0 {
  color: #fff;
}
.page .main .main_item .bot .qxdd.data-v-cb9d26b0,
.page .main .main_item .bot .lxsf.data-v-cb9d26b0 {
  background: #FFFFFF;
  border: 2rpx solid #2E80FE;
  color: #2E80FE;
}
.page .main .main_item_already.data-v-cb9d26b0 {
  padding: 28rpx 36rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
}
.page .main .main_item_already .qxdd.data-v-cb9d26b0 {
  width: 148rpx;
  height: 48rpx;
  background: #FFFFFF;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 48rpx;
  text-align: center;
  border: 2rpx solid #2E80FE;
  color: #2E80FE;
}
.page .main .main_item_already .title.data-v-cb9d26b0 {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
}
.page .main .main_item_already .ok.data-v-cb9d26b0 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #E72427;
}
.page .main .main_item_already .no.data-v-cb9d26b0 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .mid.data-v-cb9d26b0 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item_already .mid .lef.data-v-cb9d26b0 {
  display: flex;
  align-items: center;
}
.page .main .main_item_already .mid .lef image.data-v-cb9d26b0 {
  width: 120rpx;
  height: 120rpx;
}
.page .main .main_item_already .mid .lef text.data-v-cb9d26b0 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .mid .righ.data-v-cb9d26b0 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
}
.page .main .main_item_already .bot.data-v-cb9d26b0 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item_already .shifu.data-v-cb9d26b0 {
  margin-top: 20rpx;
}
.page .main .main_item_already .shifu scroll-view.data-v-cb9d26b0 {
  width: 100%;
  white-space: nowrap;
}
.page .main .main_item_already .shifu scroll-view .shifu_item.data-v-cb9d26b0 {
  display: inline-block;
  margin-right: 28rpx;
}
.page .main .main_item_already .shifu scroll-view .shifu_item image.data-v-cb9d26b0 {
  width: 92rpx;
  height: 92rpx;
  border-radius: 50%;
}
.page .main .main_item_already .shifu scroll-view .shifu_item text.data-v-cb9d26b0 {
  font-size: 22rpx;
  font-weight: 500;
  color: #E72427;
  text-align: center;
}
.page .main .main_item_already .tips.data-v-cb9d26b0 {
  width: 100%;
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  margin-top: 20rpx;
  text-align: right;
}

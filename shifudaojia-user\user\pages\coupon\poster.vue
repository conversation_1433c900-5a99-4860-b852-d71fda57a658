<template>
	<view>
		<view class="hideCanvasView">
			<wxml-to-canvas :width="652" :height="964" class="hideCanvas" ref="canvas"></wxml-to-canvas>
		</view>
		<block v-if="src">
			<image :src="src" class="code-img" @tap="previewImage"></image>
			<view class="pd-lg">
				<!-- #ifdef H5 -->
				<button class="save-btn flex-center radius" :style="{background:primaryColor}"
					@tap="previewImage">长按上图保存图片</button>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<button class="save-btn flex-center radius" :style="{background:primaryColor}"
					@tap="saveImage">保存图片至相册</button>
				<!-- #endif -->
			</view>
		</block>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	import wxmlToCanvas from "@/components/miniprogram_npm/wxml-to-canvas";
	export default {
		components: {
			wxmlToCanvas
		},
		props: {

		},
		data() {
			return {
				src: '',
				options: ''
			}
		},
		computed: mapState({
			primaryColor: state => state.config.configInfo.primaryColor,
			configInfo: state => state.config.configInfo,
			userInfo: state => state.user.userInfo,
		}),
		async onLoad(options) {
			this.options = options
			this.$util.setNavigationBarColor({
				bg: this.primaryColor
			})
			this.widget = this.$refs.canvas
			this.$util.showLoading()
			let that = this
			setTimeout(() => {
				that.renderToCanvas()
			}, 1000)
		},
		methods: {
			...mapActions(['user/report']),
			async renderToCanvas() {
				let that = this;

				let {
					nickName,
					avatarUrl
				} = this.userInfo

				let {
					qr_code,
					detail
				} = this.$util.getPage(-1)


				// let cover = 'https://lbqny.migugu.com/admin/anmo/coupon/bg.png'

				let cover = detail.share_img
				nickName = nickName.substring(0, 10)
				let width = nickName.length * 12 + 33

				let app_name = this.configInfo.app_name

				let wxml =
					`
					<view class="code-img">
						<image class="cover" src="` +
					cover +
					`"></image>
					<view class="cover abs">
						<view class="user-info">
							<text class="user-font">${nickName}</text>
						</view>
						<image class="avatar" src="` +
					avatarUrl +
					`"></image> 
					</view>
						
							<text class="qr-text">${app_name}</text> 
							<text class="qr-text">微信扫一扫立即使用</text> 
						<image class="qr" src="` +
					qr_code +
					`"></image>
					</view>
				
					`

				console.log(wxml, "wxml")

				let style = {
					rel: {
						position: 'relative'
					},
					abs: {
						position: 'absolute'
					},
					codeImg: {
						width: 326,
						height: 482,
						backgroundColor: '#fff',
						borderRadius: 15
					},
					cover: {
						width: 326,
						height: 326,
						top: 0,
						left: 0,
					},
					userInfo: {
						top: 15,
						left: 27,
						fontSize: 12,
						color: '#333',
						width,
						height: 27,
						borderRadius: 14,
						backgroundColor: '#ffffff',
						textAlign: 'center',
					},
					userFont: {
						top: 4,
						left: 8
					},
					avatar: {
						width: 32,
						height: 32,
						top: -15,
						left: 15,
						borderRadius: 16,
					},
					qrText: {
						top: 60,
						left: 25,
						width: 345,
						height: 23,
						fontSize: 14,
						textAlign: 'left',
						color: '#333'
					},
					qr: {
						width: 121,
						height: 121,
						borderRadius: 62,
						top: -28,
						left: 180
					},
				};
				const p1 = this.widget.renderToCanvas({
					wxml,
					style
				});
				console.log(p1, "===========ppppppppp1")
				p1.then(res => {
					console.log('container', res.layoutBox);
					this.container = res;
					that.extraImage()
					that.$util.hideAll()
				});
			},
			async extraImage() {
				const p2 = this.widget.canvasToTempFilePath();
				p2.then(res => {
					this.src = res.tempFilePath
				});
			},
			previewImage() {
				let finalPath = this.src;
				uni.previewImage({
					current: finalPath,
					urls: [finalPath]
				})
			},
			async saveImage() {
				await this.$util.checkAuth({
					type: "writePhotosAlbum"
				});
				let filePath = this.src;
				let [err, success] = await uni.saveImageToPhotosAlbum({
					filePath
				})
				if (err) return;
				uni.showToast({
					icon: 'none',
					title: '保存成功'
				})
			}
		},

	}
</script>

<style>
	.code-img {
		width: 652rpx;
		height: 964rpx;
		margin: 20rpx auto;
	}

	.save-btn {
		width: 490rpx;
		margin: 0 auto;
	}

	.hideCanvasView {
		position: relative;
	}

	.hideCanvas {
		position: absolute;
		left: -9999rpx;
		top: -9999rpx
	}
</style>

<block wx:if="{{detail.id}}"><view class="order-pages"><block wx:if="{{detail.pay_type==6}}"><view class="fix" style="top:-100%;left:-100%;"><min-countdown vue-id="1eb2a3bc-1" targetTime="{{detail.start_service_time_unix*1000}}" isPlay="{{true}}" bind:__l="__l"></min-countdown></view></block><view class="item-child pd-lg fill-base f-paragraph c-base" style="{{'background:'+(primaryColor)+';'}}"><view class="text-bold">{{statusType[detail.pay_type]}}</view><block wx:if="{{detail.pay_type==1&&detail.end_time>0}}"><view class="f-caption mt-sm">请在<min-countdown vue-id="1eb2a3bc-2" targetTime="{{over_time_text}}" data-event-opts="{{[['^callback',[['countEnd']]]]}}" bind:callback="__e" bind:__l="__l"></min-countdown>内完成支付，逾期未支付，订单将自动取消</view></block><view class="space-lg"></view></view><view class="menu-list flex-warp rel ml-lg mr-lg pt-lg pb-lg pl-md pr-md fill-base f-paragraph c-caption radius-16"><view class="menu-line abs b-1px-b"></view><block wx:for="{{lineList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item-child flex-center flex-column f-icontext c-paragraph" style="{{'color:'+(detail.pay_type>item.pay_type-1?primaryColor:'')+';'}}"><view class="item-img fill-base flex-center mb-sm radius" style="{{'border-color:'+(detail.pay_type>item.pay_type-1?primaryColor:'')+';'}}"><view class="{{['iconfont','_i',item.icon]}}"></view></view><view>{{item.title}}</view></view></block></view><view class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16"><view class="flex-between pb-lg"><view class="f-paragraph c-title max-380 ellipsis">预约内容</view></view><block wx:for="{{$root.l0}}" wx:for-item="aitem" wx:for-index="aindex" wx:key="aindex"><view class="{{['flex-center',[(aindex!=aitem.g0-1)?'mb-lg':'']]}}"><image class="avatar lg radius-16" mode="aspectFill" src="{{aitem.$orig.goods_cover}}"></image><view class="flex-1 ml-md"><view class="flex-between"><view class="{{['goods-title','f-title','c-title','ellipsis',[(aitem.$orig.refund_num>0)?'max-300':'']]}}">{{''+aitem.$orig.goods_name+''}}</view><block wx:if="{{aitem.$orig.refund_num>0}}"><view class="f-caption c-warning">{{"已退x"+aitem.$orig.refund_num}}</view></block></view><view class="f-caption c-caption mt-md">{{"服务时间："+detail.start_time}}</view><view class="flex-between"><view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+aitem.$orig.price+''}}</view></view><view class="c-paragraph">{{"x"+aitem.$orig.num}}</view></view></view></view></block></view><view class="mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-caption radius-16"><view class="flex-between"><view>下单人</view><view class="c-title">{{detail.address_info.user_name}}</view></view><view class="flex-between mt-md"><view>联系方式</view><view class="c-title">{{detail.address_info.mobile}}</view></view><view class="mt-md"><view>服务地址</view><view class="flex-center"><view class="c-title mt-sm flex-1 mr-md">{{''+(''+detail.address_info.address+detail.address_info.address_info)+''}}</view><view data-event-opts="{{[['tap',[['toMap',['$event']]]]]}}" class="flex-center flex-column f-icontext" style="{{'color:'+(primaryColor)+';'}}" bindtap="__e"><view class="iconfont icon-dingwei _i" style="font-size:42rpx;"></view><view>立刻导航去</view></view></view></view><block wx:if="{{detail.text}}"><view class="mt-md"><view>订单备注</view><view class="c-title mt-sm">{{detail.text}}</view></view></block></view><view class="mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-caption radius-16"><view class="flex-between mt-md"><view>下单时间</view><view class="c-title">{{detail.create_time}}</view></view><view class="flex-between mt-md"><view>服务时间</view><view class="c-title">{{detail.start_time}}</view></view><view class="flex-between mt-md"><view>服务时长</view><view class="c-title">{{detail.time_long+"分钟"}}</view></view><view class="flex-between mt-md"><view>车费详情</view><view class="flex-y-center c-title">{{carType[detail.car_type]+''}}<block wx:if="{{detail.car_type==1}}"><view class="ml-md">{{"全程"+detail.distance}}</view></block></view></view><block wx:if="{{detail.car_type==1}}"><view class="flex-between mt-md"><view>出行费用</view><view class="c-warning">{{"出租车 ¥"+detail.car_price}}</view></view></block><view class="flex-between mt-md"><view>服务项目费用</view><view class="c-warning">{{"¥"+detail.init_service_price}}</view></view><block wx:if="{{detail.discount*1>0}}"><view class="flex-between mt-md"><view>卡券优惠</view><view class="c-warning">{{"-¥"+detail.discount}}</view></view></block><view class="flex-between mt-md"><view>支付方式</view><view class="flex-y-baseline c-title"><view class="{{['iconfont','mr-sm','_i',payType[detail.is_balance].icon]}}" style="{{'color:'+(detail.is_balance==1?primaryColor:'')+';'}}"></view>{{payType[detail.is_balance].text+''}}</view></view><view class="flex-between mt-md pt-md b-1px-t"><view></view><view class="flex-y-baseline c-title">总计：<view class="c-warning">{{"¥"+detail.pay_price}}</view></view></view></view><view class="mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-caption radius-16"><view class="flex-between pb-lg c-title">{{"订单编号："+detail.order_code+''}}</view><timeline vue-id="1eb2a3bc-3" list="{{lineList}}" info="{{detail}}" bind:__l="__l"></timeline></view><view class="space-max-footer"></view><block wx:if="{{$root.g1}}"><view class="footer-info fix fill-base"><view class="flex-between pd-lg"><view></view><view class="flex-center f-desc c-title"><block wx:if="{{detail.pay_type==2}}"><block><button data-event-opts="{{[['tap',[['toRefuse',['$event']]]]]}}" class="clear-btn order" catchtap="__e">拒绝接单</button><button data-event-opts="{{[['tap',[['toConfirm',[3]]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')+('border-color:'+(primaryColor)+';')}}" catchtap="__e">确认接单</button></block></block><block wx:if="{{detail.pay_type==3||detail.pay_type==4||detail.pay_type==5}}"><block><button data-event-opts="{{[['tap',[['toTel',['$event']]]]]}}" class="clear-btn order" catchtap="__e">咨询</button><button data-event-opts="{{[['tap',[['toConfirm',[detail.pay_type*1+1]]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')+('border-color:'+(primaryColor)+';')}}" catchtap="__e">{{detail.pay_type==3?'已出发':detail.pay_type==4?'拍照确认到达':'开始服务'}}</button></block></block><block wx:if="{{detail.pay_type==6}}"><block><button data-event-opts="{{[['tap',[['toConfirm',[7]]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')+('border-color:'+(primaryColor)+';')}}" catchtap="__e">拍照完成服务</button></block></block></view></view><view class="space-safe"></view></view></block><uni-popup class="vue-ref" vue-id="1eb2a3bc-4" type="center" custom="{{true}}" data-ref="refuse_item" bind:__l="__l" vue-slots="{{['default']}}"><view class="common-popup-content fill-base pd-lg radius-34"><view class="title">拒绝接单</view><view class="desc">请确认是否拒接接单</view><textarea class="pd-lg textarea f-desc c-title mt-lg radius-20" maxlength="200" placeholder="请输入拒单原因" placeholder-class="f-desc c-caption" data-event-opts="{{[['input',[['__set_model',['','coach_refund_text','$event',[]]]]]]}}" value="{{coach_refund_text}}" bindinput="__e"></textarea><view class="flex-center mt-md" style="width:540rpx;"><view class="flex-1"></view><view>{{($root.g2>200?200:$root.g3)+"/200"}}</view></view><view class="button"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="item-child" catchtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmRefuse',['$event']]]]]}}" class="item-child c-base" style="{{'background:'+(primaryColor)+';'+('color:'+('#fff')+';')}}" catchtap="__e">确定</view></view></view></uni-popup></view></block>
<block wx:if="{{detail.id}}"><view class="user-order-refund"><view class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16"><view class="flex-between pb-lg b-1px-b"><view class="f-paragraph c-title max-380 ellipsis">{{"订单号："+detail.order_code}}</view><view class="f-caption text-bold" style="{{'color:'+(detail.pay_type==2?primaryColor:detail.pay_type<6?subColor:detail.pay_type==6?'#11C95E':'#333')+';'}}">{{''+statusType[detail.pay_type]+''}}</view></view><block wx:for="{{detail.order_goods}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{item.can_refund_num>0}}"><view data-event-opts="{{[['tap',[['handerRadioChange',[index]]]]]}}" class="item-child flex-center mt-lg" bindtap="__e"><view class="{{['iconfont','mr-md','_i',[(!item.checked)?'icon-xuanze':''],[(item.checked)?'icon-xuanze-fill':'']]}}" style="{{'color:'+(item.checked?primaryColor:'')+';'}}"></view><view class="flex-1"><view class="flex-center"><image class="goods-img radius-16" mode="aspectFill" src="{{item.goods_cover}}"></image><view class="flex-1 ml-md max-380"><view class="goods-title f-title c-title text-bold ellipsis">{{item.goods_name}}</view><view class="f-caption c-caption">{{"服务技师："+detail.coach_info.coach_name}}</view><view class="f-caption c-caption">{{detail.start_time}}</view><view class="flex-between"><view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+item.true_price+''}}</view></view><block wx:if="{{item.can_refund_num>1}}"><view class="flex-warp"><button data-event-opts="{{[['tap',[['changeNum',[index,-1]]]]]}}" class="reduce" style="{{'border-color:'+(primaryColor)+';'+('color:'+(primaryColor)+';')}}" catchtap="__e"><view class="iconfont icon-jian-bold _i"></view></button><button class="addreduce clear-btn">{{item.apply_num}}</button><button data-event-opts="{{[['tap',[['changeNum',[index,1]]]]]}}" class="add" style="{{'background:'+(primaryColor)+';'+('border-color:'+(primaryColor)+';')}}" catchtap="__e"><view class="iconfont icon-jia-bold _i"></view></button></view></block><block wx:else><view class="c-paragraph">{{"x"+item.apply_num}}</view></block></view></view></view></view></view></block></block></block></view><view class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16"><view class="flex-between pb-lg f-title c-title text-bold">退款原因</view><view class="textarea-info f-caption c-caption radius-16"><textarea class="input-textarea f-paragraph pd-lg" placeholder-class="f-paragraph" maxlength="300" placeholder="输入退款原因" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['form']]]]]}}" value="{{form.text}}" bindinput="__e"></textarea><view class="text-right pb-lg pr-lg">{{($root.g0>300?300:$root.g1)+"/300"}}</view></view></view><view class="item-child mt-md ml-lg mr-lg pt-lg pl-lg pr-lg fill-base radius-16"><view class="flex-between pb-sm f-title c-title text-bold">上传图片</view><view class="flex-between pt-sm"><upload vue-id="6a28eadc-1" imagelist="{{form.imgs}}" imgtype="imgs" imgclass="mini" text="添加照片" imgsize="{{5}}" data-event-opts="{{[['^del',[['imgDel']]],['^upload',[['imgUpload']]]]}}" bind:del="__e" bind:upload="__e" bind:__l="__l"></upload></view><block wx:if="{{$root.g2==0}}"><view class="space-lg"></view></block><block wx:else><view class="space-sm"></view></block></view><view class="space-max-footer"></view><view class="refund-bottom-info fix pl-lg pr-lg"><view class="flex-between"><view data-event-opts="{{[['tap',[['selectAllItem',['$event']]]]]}}" class="flex-y-center" bindtap="__e"><view class="{{['iconfont','mr-sm','_i',[(!selectAll)?'icon-xuanze':''],[(selectAll)?'icon-xuanze-fill':'']]}}" style="{{'color:'+(selectAll?primaryColor:'')+';'}}"></view>全选</view><view class="text-right flex-center"><view class="flex-y-center"><view class="f-caption c-caption mr-sm">{{"共"+total_refund_num+"件"}}</view>退款金额<view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+total_refund_price+''}}</view></view></view><button data-event-opts="{{[['tap',[['toSubmit',['$event']]]]]}}" class="clear-btn order" style="{{'color:'+('#fff')+';'+('background:'+(primaryColor)+';')}}" catchtap="__e">提交申请</button></view></view></view></view></block>
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-1653f848 {
  padding: 0 30rpx;
}
.page .site_item.data-v-1653f848 {
  margin-top: 40rpx;
  height: 168rpx;
  display: flex;
}
.page .site_item image.data-v-1653f848 {
  width: 182rpx;
  height: 136rpx;
  margin-right: 20rpx;
}
.page .site_item .content.data-v-1653f848 {
  flex: 1;
  height: 100%;
  border-bottom: 2rpx solid #E9E9E9;
}
.page .site_item .content .name.data-v-1653f848 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}
.page .site_item .content .address.data-v-1653f848 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 28rpx;
}
.page .site_item .content .address .position.data-v-1653f848 {
  display: flex;
  align-items: center;
}
.page .site_item .content .address .position text.data-v-1653f848 {
  font-size: 20rpx;
  font-weight: 400;
  color: #333333;
  max-width: 300rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .site_item .content .address image.data-v-1653f848 {
  width: 39rpx;
  height: 39rpx;
  margin: 0;
}

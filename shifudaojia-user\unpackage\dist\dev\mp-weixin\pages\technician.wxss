@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-5d289ec6 {
  height: 100vh;
  overflow: auto;
  background-color: #F8F8F8;
  padding-bottom: 80rpx;
}
.page .header.data-v-5d289ec6 {
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  background-color: #FFF;
  height: 10vh;
}
.page .main.data-v-5d289ec6 {
  margin: 0 auto;
  width: 690rpx;
  height: 90vh;
  display: flex;
}
.page .main .left.data-v-5d289ec6 {
  width: 152rpx;
}
.page .main .left .scrollL.data-v-5d289ec6 {
  height: 100%;
}
.page .main .left .scrollL .left_item.data-v-5d289ec6 {
  height: 100rpx;
  line-height: 100rpx;
  text-align: left;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .main .right.data-v-5d289ec6 {
  flex: 1;
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  padding-bottom: 120rpx;
}
.page .main .right .threeBox.data-v-5d289ec6 {
  height: 100%;
  padding: 24rpx;
}
.page .main .right .threeBox .title.data-v-5d289ec6 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .main .right .threeBox .three_item.data-v-5d289ec6 {
  position: relative;
  border-bottom: 2rpx solid #E9E9E9;
  padding: 32rpx 0;
}
.page .main .right .threeBox .three_item image.data-v-5d289ec6 {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 0;
  bottom: 46rpx;
}
.page .main .right .threeBox .three_item .top text.data-v-5d289ec6,
.page .main .right .threeBox .three_item .bottom text.data-v-5d289ec6 {
  font-size: 24rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 18rpx;
}
.page .main .right .threeBox .three_item .bottom.data-v-5d289ec6 {
  margin-top: 20rpx;
}
.page .main .right .scrollR.data-v-5d289ec6 {
  height: 100%;
}
.page .main .right .right_box.data-v-5d289ec6 {
  padding: 24rpx 9rpx;
}
.page .main .right .right_box .title.data-v-5d289ec6 {
  padding-left: 15rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}
.page .main .right .right_box .img.data-v-5d289ec6 {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.page .main .right .right_box .img .img_item.data-v-5d289ec6 {
  margin: 10rpx 15rpx;
}
.page .main .right .right_box .img .img_item image.data-v-5d289ec6 {
  width: 144rpx;
  height: 144rpx;
}
.page .main .right .right_box .img .img_item .lname.data-v-5d289ec6 {
  margin-top: 12rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  text-align: center;
}

<view class="page data-v-cb9d26b0"><view class="header data-v-cb9d26b0"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleHeader',['$0'],[[['list','',index]]]]]]]}}" class="header_item data-v-cb9d26b0" bindtap="__e"><view style="{{(currentIndex==item.value?'color:#2E80FE;':'')}}" class="data-v-cb9d26b0">{{item.name}}</view><view class="blue data-v-cb9d26b0" style="{{(currentIndex==item.value?'':'background-color:#fff;')}}"></view></view></block></view><block wx:if="{{$root.g0}}"><u-empty vue-id="a806ad44-1" mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png" class="data-v-cb9d26b0" bind:__l="__l"></u-empty></block><view class="main data-v-cb9d26b0"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goUrl',['/pages/order_details?id='+item.$orig.id]]]]]}}" class="main_item data-v-cb9d26b0" bindtap="__e"><view class="head data-v-cb9d26b0"><view class="no data-v-cb9d26b0">{{"单号："+item.$orig.order_code}}</view><view class="type data-v-cb9d26b0">{{item.$orig.pay_type==-1?'已取消':pay_typeArr[item.$orig.pay_type]}}</view></view><view class="mid data-v-cb9d26b0"><view class="lef data-v-cb9d26b0"><image src="{{item.$orig.order_goods[0].goods_cover}}" mode class="data-v-cb9d26b0"></image><text class="data-v-cb9d26b0">{{item.$orig.order_goods[0].goods_name}}</text></view><block wx:if="{{item.$orig.type==0||item.$orig.type==1&&item.$orig.pay_type>=1}}"><view class="righ data-v-cb9d26b0"><view class="data-v-cb9d26b0">{{'￥'+(item.$orig.type==0?item.$orig.order_goods[0].price:item.$orig.quoted_price[item.g1].price)+''}}</view><view class="data-v-cb9d26b0">{{"x"+item.$orig.order_goods[0].num}}</view></view></block></view><view class="bot data-v-cb9d26b0"><text class="data-v-cb9d26b0">{{item.$orig.create_time}}</text><block wx:if="{{item.$orig.pay_type==1}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="qzf data-v-cb9d26b0" catchtap="__e">去支付</view></block><block wx:if="{{item.$orig.pay_type>=2&&item.$orig.pay_type!=7}}"><view data-event-opts="{{[['tap',[['confirmorder',['$0'],[[['orderList1','',index]]]]]]]}}" class="qrwc data-v-cb9d26b0" catchtap="__e">确认完成</view></block><block wx:if="{{item.$orig.pay_type>=2&&item.$orig.pay_type!=7}}"><view data-event-opts="{{[['tap',[['applyT',['$0'],[[['orderList1','',index]]]]]]]}}" class="qrwc data-v-cb9d26b0" catchtap="__e">申请退款</view></block><block wx:if="{{item.$orig.pay_type==7&&item.$orig.is_comment!=1}}"><view data-event-opts="{{[['tap',[['goevaluate',['$0'],[[['orderList1','',index]]]]]]]}}" class="qpl data-v-cb9d26b0" catchtap="__e">去评价</view></block></view></view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goChoose',['$0'],[[['orderList2','',index]]]]]]]}}" class="main_item_already data-v-cb9d26b0" bindtap="__e"><view class="title data-v-cb9d26b0">{{item.g2==0?'等待师傅报价':'等待您选择师傅'}}</view><block wx:if="{{item.g3>0}}"><view class="ok data-v-cb9d26b0">已有师傅报价</view></block><view class="no data-v-cb9d26b0">{{"单号："+item.$orig.order_code}}</view><view class="mid data-v-cb9d26b0"><view class="lef data-v-cb9d26b0"><image src="{{item.$orig.order_goods[0].goods_cover}}" mode class="data-v-cb9d26b0"></image><text class="data-v-cb9d26b0">{{item.$orig.order_goods[0].goods_name}}</text></view></view><view class="bot data-v-cb9d26b0"><text class="data-v-cb9d26b0">{{item.$orig.create_time}}</text></view><view class="shifu data-v-cb9d26b0"><scroll-view scroll-x="true" class="data-v-cb9d26b0"><block wx:for="{{item.$orig.quoted_price}}" wx:for-item="shfItem" wx:for-index="shfIndex" wx:key="shfIndex"><view class="shifu_item data-v-cb9d26b0"><image src="{{shfItem.self_img}}" mode class="data-v-cb9d26b0"></image><text class="data-v-cb9d26b0">{{"￥"+shfItem.price}}</text></view></block></scroll-view></view><view data-event-opts="{{[['tap',[['cancelorder',['$0'],[[['orderList2','',index]]]]]]]}}" class="qxdd data-v-cb9d26b0" catchtap="__e">取消订单</view><block wx:if="{{item.g4>0}}"><view class="tips data-v-cb9d26b0">{{item.g5+"位师傅已报价"}}</view></block></view></block></view><u-modal vue-id="a806ad44-2" show="{{showCancel}}" title="取消订单" content="确认要取消该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e1']]],['^confirm',[['confirmCancel']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-cb9d26b0" bind:__l="__l"></u-modal><u-modal vue-id="a806ad44-3" show="{{showConfirm}}" title="完成订单" content="确认要完成该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e2']]],['^confirm',[['confirmconfirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-cb9d26b0" bind:__l="__l"></u-modal><block wx:if="{{!($root.g6+$root.g7<10)}}"><view style="display:flex;justify-content:center;" class="data-v-cb9d26b0"><u-loadmore vue-id="a806ad44-4" status="{{status}}" class="data-v-cb9d26b0" bind:__l="__l"></u-loadmore></view></block></view>
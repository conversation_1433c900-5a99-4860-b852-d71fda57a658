<template>
	<view class="order-pages" v-if="orderInfo.coach_id">


		<view v-if="orderInfo.order_goods && orderInfo.order_goods.length > 0">

			<view @tap.stop="$util.goUrl({url:`/user/pages/address/list?check=1`})"
				class="address-info flex-warp mt-lg ml-lg mr-lg pd-lg fill-base radius-16">
				<view class="address-icon flex-center c-base radius"
					:style="{background:`linear-gradient(to right, ${subColor}, ${primaryColor})`}"><i
						class="iconfont iconjuli"></i></view>
				<view class="flex-1 flex-between ml-md">
					<view class="max-500">
						<block v-if="orderInfo.address_info.id">
							<view class="flex-y-baseline username c-title text-bold">
								{{orderInfo.address_info.user_name}}
								<view class="ml-md f-desc c-caption">{{orderInfo.address_info.mobile}}</view>
							</view>
							<view class="f-desc c-title ellipsis">
								{{`${orderInfo.address_info.address} ${orderInfo.address_info.address_info}`}}
							</view>
						</block>
						<block v-else>
							<view class="username c-title text-bold">请选择地址</view>
						</block>
					</view>
					<i class="iconfont icon-right"></i>
				</view>
			</view>


			<view class="mt-md ml-lg mr-lg fill-base radius-16">
				<view
					@tap.stop="$util.goUrl({url:`/user/pages/choose-time?id=${options.id}&index=${send_info.time_index}`})"
					class="flex-between pt-lg pb-lg pl-lg pr-md b-1px-b">
					<view class="f-title c-title text-bold">服务时间</view>
					<view class="flex-y-center f-paragraph c-caption ml-sm">
						<view class="c-caption mr-sm">{{send_info.time.time || '请选择预约时间'}}</view>
						<i class="iconfont icon-right"></i>
					</view>
				</view>
				<view class="flex-between pd-lg">
					<view class="f-title c-title text-bold">出行方式</view>
					<view class="flex-center">
						<view @tap.stop="toChangeItem(index)" class="flex-y-center" :class="[{'mr-lg':index==0}]"
							:style="{color:carTypeInd == index ? primaryColor:''}" v-for="(item,index) in carTypeList"
							v-show="item.id == 1 || (isBus == 1 && item.id == 0)" :key="index"><i
								class="iconfont icon-xuanze mr-sm"
								:class="[{'icon-xuanze-fill':carTypeInd == index}]"></i>{{item.title}}
						</view>
					</view>
				</view>
			</view>
			<view class="mt-md ml-lg mr-lg  fill-base radius-16">
				<view class="list-item flex-center pd-lg" :class="[{'b-1px-t':index!=0}]"
					v-for="(item,index) in orderInfo.order_goods" :key="index">
					<image mode="aspectFill" class="item-img radius-16" :src="item.cover"></image>
					<view class="flex-1 ml-md">
						<view class="f-title c-title ellipsis">{{item.title}}</view>
						<view class="flex-y-center f-desc c-caption">
							<view class="f-title c-warning mr-sm">¥{{item.price}}</view>/ {{item.time_long}}分钟
						</view>
						<view class="flex-between">
							<view class="f-caption c-caption mt-sm mb-sm max-300">
								服务技师：{{orderInfo.coach_info.coach_name}}
							</view>
							<view class="flex-warp">
								<button @tap.stop="changeNum(-1,index)" class="reduce"
									:style="{borderColor:primaryColor,color:primaryColor}"><i
										class="iconfont icon-jian-bold"></i></button>
								<button class="addreduce clear-btn">{{item.num || 0}}</button>
								</block>
								<button @tap.stop="changeNum(1,index)" class="add"
									:style="{background:primaryColor,borderColor:primaryColor}"><i
										class="iconfont icon-jia-bold"></i></button>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="mt-md ml-lg mr-lg fill-base radius-16">
				<view @tap.stop="$util.goUrl({url:`/user/pages/coupon/use`})"
					class="flex-between pt-lg pb-lg pl-lg pr-md"
					:class="[{'b-1px-b':carTypeList[carTypeInd].id === 1}]">
					<view class="f-title c-title text-bold">卡券优惠</view>
					<view class="flex-y-center f-paragraph c-caption ml-sm">
						<view class="c-warning mr-sm">
							{{orderInfo.discount ? `-¥${orderInfo.discount}` : `${orderInfo.canUseCoupon}张可用`}}
						</view>
						<i class="iconfont icon-right"></i>
					</view>
				</view>
				<block v-if="carTypeList[carTypeInd].id === 1">
					<view class="flex-between pd-lg">
						<view class="f-title c-title text-bold">往返车费</view>
						<view class="f-paragraph c-caption c-warning">¥{{orderInfo.car_price}}</view>
					</view>
					<view class="pl-lg pr-lg pb-lg f-caption c-caption">
						全程共{{orderInfo.distance}}，出租出行{{orderInfo.car_config.start_distance}}公里内，起步{{orderInfo.car_config.start_price}}元。里程计价：{{orderInfo.car_config.distance_price}}元/公里
					</view>
				</block>
			</view>

			<view class="mt-md ml-lg mr-lg pd-lg fill-base radius-16">
				<view class="flex-between pb-lg">
					<view class="flex-y-baseline">
						<view class="f-title c-title text-bold">订单备注</view>
						<view class="f-paragraph c-caption ml-sm">(选填)</view>
					</view>
				</view>
				<view class="f-caption c-caption fill-body radius-16">
					<textarea v-model="form.text" class="item-textarea f-paragraph pd-lg"
						placeholder-class="f-paragraph" maxlength="100" placeholder="输入订单备注" />
					<view class="text-right pb-lg pr-lg">{{form.text.length>100?100:form.text.length}}/100
					</view>
				</view>
			</view>


			<view class="mt-md ml-lg mr-lg fill-base radius-16">
				<view @tap.stop="toChangeItem(index,2)" class="flex-between pt-lg pb-lg pl-lg pr-md b-1px-b"
					v-for="(item,index) in payList" :key="index">
					<view class="flex-y-center f-title c-title">
						<i class="iconfont mr-md" :class="item.icon"
							:style="{color:item.id==1?primaryColor:'',fontSize:'50rpx'}"></i>
						{{item.title}}
						<view class="f-paragraph c-caption ml-md" v-if="item.id == 1">余额{{balance || 0}}元</view>
					</view>
					<view class="flex-y-center" :style="{color:payInd == index ? primaryColor:''}">
						<i class="iconfont icon-xuanze mr-sm" :class="[{'icon-xuanze-fill':payInd == index}]"></i>
					</view>
				</view>
			</view>


			<view @tap.stop="isAgree=!isAgree"
				class="mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-title flex-y-center radius-16"
				v-if="configInfo.trading_rules && configInfo.trading_rules.length>0">
				<i class="iconfont mr-sm" :class="isAgree?'icon-xuanze-fill':'icon-xuanze'"
					:style="{color:isAgree?primaryColor:''}"></i>
				我已阅读并同意<view @tap.stop="$refs.show_rule_item.open()" :style="{color:primaryColor}">《平台交易规则》</view>
			</view>

			<view class="space-max-footer"></view>

			<view class="pay-info fix flex-between text-right pl-lg pr-lg fill-base">
				<view class="flex-y-center f-paragraph c-title text-bold ml-sm mr-lg">合计：
					<view class="flex-y-baseline f-title c-warning">¥{{orderInfo.pay_price}}</view>
				</view>
				<auth :needAuth="userInfo && (!userInfo.phone || !userInfo.nickName)" :must="true"
					:type="!userInfo.phone ? 'phone' : 'userInfo'" @go="toPay" style="width: 182rpx;">
					<view class="pay-btn flex-center f-paragraph c-base radius" :style="{background:primaryColor}">
						立即支付</view>
				</auth>
			</view>


		</view>

		<abnor @confirm="$util.goUrl({url:`/pages/service`,openType:`reLaunch`})" :tip="[{ text: '该服务已下架~', color: 0 }]"
			:button="[{ text: '去看看其他服务' , type: 'confirm' }]" btnSize="" v-else>
		</abnor>

		<uni-popup ref="show_rule_item" type="center" :maskClick="false">
			<view class="popup-rule">
				<view class="fill-base pd-lg radius-26">
					<view class="f-title c-title text-bold flex-center pd-lg">平台交易规则</view>
					<scroll-view scroll-y @touchmove.stop.prevent class="rule-text">
						<parser :html="configInfo.trading_rules" @linkpress="linkpress" show-with-animation lazy-load>
							加载中...
						</parser>
					</scroll-view>
				</view>
				<view @tap="$refs.show_rule_item.close()" class="flex-center pd-lg"><i
						class="iconfont icon-close c-base"></i></view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions,
		mapMutations
	} from "vuex"
	import parser from "@/components/jyf-Parser/index"
	export default {
		components: {
			parser
		},
		data() {
			return {
				carTypeList: [{
					id: 1,
					title: '出租车'
				}, {
					id: 0,
					title: '公交/地铁'
				}, ],
				carTypeInd: 0,
				payList: [{
					id: 0,
					icon: 'iconweixinzhifu1 c-success',
					title: '微信支付'
				}, {
					id: 1,
					icon: 'iconqianbao',
					title: '账户余额',
					is_disabled: false
				}],
				payInd: 0,
				options: {},
				balance: 0,
				send_info: {
					time_index: 0,
					time: {}
				},
				orderInfo: {
					coupon_id: 0,
					address_info: {
						id: 0
					}
				},
				form: {
					text: ''
				},
				lockTap: false,
				isBus: 0,
				isAgree: false
			}
		},
		computed: mapState({
			primaryColor: state => state.config.configInfo.primaryColor,
			subColor: state => state.config.configInfo.subColor,
			configInfo: state => state.config.configInfo,
			commonOptions: state => state.user.commonOptions,
			userInfo: state => state.user.userInfo,
			mineInfo: state => state.user.mineInfo,
			carList: state => state.order.carList,
		}),
		watch: {
			'send_info.time.time'(value) {
				console.log(value, "time")
				let {
					time_str: start_time
				} = this.send_info.time
				console.log(start_time)
				this.getIsBusCall(start_time)
			}
		},
		async onLoad(options) {
			this.options = options
			await this.initIndex()
			this.getIsBusCall('')
		},
		methods: {
			...mapActions(['getConfigInfo', 'getMineInfo', 'getCarList']),
			...mapMutations(['updateUserItem']),
			async initIndex(refresh = false) {
				// #ifdef H5
				if (!refresh && this.$jweixin.isWechat()) {
					await this.$jweixin.initJssdk();
					this.$jweixin.wxReady(() => {
						this.$jweixin.hideOptionMenu()
					})
				}
				// #endif
				if (!this.configInfo.id || refresh) {
					await this.getConfigInfo()
				}
				let {
					id: coach_id,
					ser_id = 0
				} = this.options
				let {
					coupon_id,
					address_info
				} = this.orderInfo
				let {
					id: address_id
				} = address_info
				if (!address_id) {
					let address_info = await this.$api.mine.getDefultAddress()
					address_id = address_info && address_info.id ? address_info.id : null
				}
				let car_type = this.carTypeList[this.carTypeInd].id
				let [data, mineInfo] = await Promise.all([this.$api.order.payOrderInfo({
					service_id: ser_id,
					coach_id,
					car_type,
					coupon_id,
					address_id
				}), this.getMineInfo()])
				this.$util.setNavigationBarColor({
					bg: this.primaryColor
				})
				data.address_info = data.address_info.id ? data.address_info : {}
				this.orderInfo = data
				let {
					balance
				} = this.mineInfo
				this.payList[1].is_disabled = balance * 1 < this.orderInfo.pay_price * 1
				this.balance = balance
			},
			initRefresh() {
				this.initIndex(true)
			},
			linkpress(res) {
				console.log("linkpress", res);
				// #ifdef APP-PLUS
				this.$util.goUrl({
					url: res.href,
					openType: 'web'
				})
				// #endif
			},
			async getIsBusCall(start_time) {
				this.isBus = await this.$api.mine.getIsBus({
					start_time
				})
				if (this.isBus == 0) {
					this.carTypeInd = 0
					await this.initRefresh()
				}
			},
			// 选择出行方式/支付方式
			async toChangeItem(index, key = 1) {
				let {
					address_info
				} = this.orderInfo
				if (key == 1) {
					if (index == 1 && !address_info.id) {
						this.$util.showToast({
							title: `请选择地址`
						})
						return
					}
					if (index == 1 && !this.send_info.time.time) {
						this.$util.showToast({
							title: `请选择时间`
						})
						return
					}
					this.carTypeInd = index
					await this.initRefresh()
				} else {
					if (index == 1 && this.payList[1].is_disabled) return
					this.payInd = index
				}
			},
			// 加/减数量
			async changeNum(mol, index) {
				let {
					id,
					service_id
				} = this.orderInfo.order_goods[index]
				let {
					id: coach_id
				} = this.options
				if (this.lockTap) return;
				this.lockTap = true;
				let methodModel = mol > 0 ? 'addCar' : 'delCar'
				let param = mol > 0 ? {
					service_id,
					coach_id,
					num: 1
				} : {
					id,
					num: 1
				}
				try {
					let changeNum = await this.$api.order[methodModel](param)
					await this.getCarList({
						coach_id
					})
					await this.initRefresh()
					this.lockTap = false
				} catch (e) {
					this.lockTap = false
				}
			},
			checkInput(e, key) {
				let val = this.$util.formatMoney(e.detail.value)
				this.$nextTick(() => {
					this.form[key] = val
				})
			},
			async toPay() {
				let {
					coupon_id = 0,
						address_info = {}
				} = this.orderInfo
				let {
					id: address_id
				} = address_info
				if (!address_id) {
					this.$util.showToast({
						title: `请选择地址`
					})
					return
				}
				let {
					carTypeList,
					carTypeInd,
					payList,
					payInd,
					send_info
				} = this
				if (!send_info.time_index) {
					this.$util.showToast({
						title: `请选择时间`
					})
					return
				}
				let {
					trading_rules
				} = this.configInfo
				if (trading_rules && trading_rules.length > 0 && !this.isAgree) {
					this.$util.showToast({
						title: `请先阅读并同意《平台交易规则》`
					})
					return
				}
				let {
					id: car_type
				} = carTypeList[carTypeInd]
				let {
					id: is_balance
				} = payList[payInd]
				let {
					id: coach_id
				} = this.options
				let {
					time_str: start_time
				} = send_info.time

				let {
					channel_id = 0
				} = this.commonOptions
				let param = this.$util.deepCopy(this.form)
				param = Object.assign({}, param, {
					coach_id,
					coupon_id,
					address_id,
					car_type,
					is_balance,
					start_time,
					channel_id
				})
				if (this.lockTap) return
				this.lockTap = true
				this.$util.showLoading()
				try {
					let {
						pay_list
					} = await this.$api.order.payOrder(param)
					this.$util.hideAll()
					// console.log({pay_list});mweb_url
					if(pay_list.mweb_url){
						this.$util.goUrl({
							url: '/pages/order?mweb_url='+encodeURIComponent(pay_list.mweb_url),
							openType: `reLaunch`
						})
						return 
					}
					if (pay_list) {
						try {
							await this.$util.pay(pay_list)
							this.$util.showToast({
								title: `支付成功`
							})
							setTimeout(() => {
								this.$util.goUrl({
									url: '/pages/order?tab=2',
									openType: `reLaunch`
								})
							}, 1000)
							this.lockTap = false
							return
						} catch (e) {
							this.$util.showToast({
								title: `支付失败`
							})
							setTimeout(() => {
								this.$util.goUrl({
									url: '/pages/order?tab=1',
									openType: `reLaunch`
								})
							}, 1000)
							this.lockTap = false
							return
						}
					}
					this.$util.showToast({
						title: `支付成功`
					})
					setTimeout(() => {
						this.$util.goUrl({
							url: '/pages/order?tab=2',
							openType: `reLaunch`
						})
					}, 1000)
				} catch (e) {
					setTimeout(() => {
						this.lockTap = false
						this.$util.hideAll()
					}, 2000)
				}
			}
		}
	}
</script>


<style lang="scss">
	.order-pages {
		.list-item {
			.item-img {
				width: 140rpx;
				height: 140rpx;
				background: #f4f6f8;
			}

			.ellipsis {
				max-width: 466rpx;
			}

			.item-tag {
				width: 100rpx;
				height: 36rpx;
				margin-top: -18rpx;
			}

			.iconyduixingxingshixin {
				font-size: 28rpx;
			}

			.item-btn {
				width: 129rpx;
				height: 54rpx;
			}
		}

		.pay-info {
			height: 110rpx;
			bottom: 0;
			height: calc(110rpx + env(safe-area-inset-bottom) / 2);
			padding-bottom: calc(env(safe-area-inset-bottom) / 2);

			.pay-btn {
				width: 182rpx;
				height: 74rpx;
			}
		}

		.popup-rule {
			width: 680rpx;
			height: auto;

			.rule-text {
				min-height: 300rpx;
				max-height: 60vh;
			}

			.iconfont {
				font-size: 60rpx;
			}
		}
	}
</style>

<block wx:if="{{isLoad}}"><view class="apply-pages"><view class="apply-form"><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg b-1px-b"><view class="item-text">您的姓名</view><input class="item-input flex-1" type="text" maxlength="20" placeholder="{{rule[0].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','user_name','$event',[]],['form']]]]]}}" value="{{form.user_name}}" bindinput="__e"/></view><view class="flex-between pl-lg pr-lg b-1px-b"><view class="item-text">手机号码</view><input class="item-input flex-1" type="text" placeholder="{{rule[1].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e"/></view></view><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg"><view class="item-text">备注信息</view><input class="item-input flex-1" disabled="{{true}}" type="text"/></view><textarea class="item-textarea pd-lg" maxlength="300" placeholder="输入备注信息" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['form']]]]]}}" value="{{form.text}}" bindinput="__e"></textarea><view class="text-right pb-lg pr-lg">{{''+($root.g0>300?300:$root.g1)+'/300'}}</view></view></view><view class="space-max-footer"></view><auth vue-id="c6987ddc-1" needAuth="{{userInfo&&!userInfo.nickName}}" must="{{true}}" type="userInfo" data-event-opts="{{[['^go',[['submit']]]]}}" bind:go="__e" bind:__l="__l" vue-slots="{{['default']}}"><fix-bottom-button vue-id="{{('c6987ddc-2')+','+('c6987ddc-1')}}" text="{{[{text:'确定申请',type:'confirm'}]}}" bgColor="#fff" bind:__l="__l"></fix-bottom-button></auth></view></block>
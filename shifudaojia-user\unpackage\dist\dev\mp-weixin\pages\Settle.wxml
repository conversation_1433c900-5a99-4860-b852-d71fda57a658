<view class="page data-v-6a3401a4"><block wx:if="{{flag}}"><u-picker vue-id="4766983c-1" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeHandler']]],['^cancel',[['e0']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-6a3401a4 vue-ref" bind:__l="__l"></u-picker></block><u-modal vue-id="4766983c-2" show="{{showMoney}}" showCancelButton="{{true}}" cancelText="再想想" data-event-opts="{{[['^confirm',[['confirmMoney']]],['^cancel',[['e1']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-6a3401a4" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-6a3401a4"><rich-text nodes="{{contentMoney}}" class="data-v-6a3401a4"></rich-text></view></u-modal><u-modal vue-id="4766983c-3" show="{{show}}" title="{{title}}" showCancelButton="{{true}}" confirmText="同意" cancelText="不同意" data-event-opts="{{[['^confirm',[['confirmModel']]],['^cancel',[['cancelModel']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-6a3401a4" bind:__l="__l" vue-slots="{{['default']}}"><view class="slot-content data-v-6a3401a4"><rich-text nodes="{{entry_notice}}" class="data-v-6a3401a4"></rich-text></view></u-modal><block wx:if="{{shInfo.status==4}}"><u-modal vue-id="4766983c-4" show="{{showSh}}" title="驳回原因" confirmText="确定" content="{{shInfo.sh_text}}" data-event-opts="{{[['^confirm',[['e2']]]]}}" bind:confirm="__e" class="data-v-6a3401a4" bind:__l="__l"></u-modal></block><block wx:if="{{shInfo.status}}"><view data-event-opts="{{[['tap',[['shDetail',['$event']]]]]}}" class="header data-v-6a3401a4" style="{{('color:'+arr[shInfo.status-1].color)}}" bindtap="__e">{{''+arr[shInfo.status-1].text+''}}</view></block><view class="main data-v-6a3401a4"><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>姓名</view><input type="text" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','coach_name','$event',[]],['form']]]]]}}" value="{{form.coach_name}}" bindinput="__e" class="data-v-6a3401a4"/></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>手机号</view><input type="text" placeholder="请输入手机号" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e" class="data-v-6a3401a4"/></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>性别</view><u-radio-group bind:input="__e" vue-id="4766983c-5" placement="row" value="{{form.sex}}" data-event-opts="{{[['^input',[['__set_model',['$0','sex','$event',[]],['form']]]]]}}" class="data-v-6a3401a4" bind:__l="__l" vue-slots="{{['default']}}"><u-radio vue-id="{{('4766983c-6')+','+('4766983c-5')}}" customStyle="{{({marginRight:'20px'})}}" label="男" name="{{0}}" class="data-v-6a3401a4" bind:__l="__l"></u-radio><u-radio vue-id="{{('4766983c-7')+','+('4766983c-5')}}" label="女" name="{{1}}" class="data-v-6a3401a4" bind:__l="__l"></u-radio></u-radio-group></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>从业年份</view><input type="text" placeholder="请输入从业年份" data-event-opts="{{[['input',[['__set_model',['$0','work_time','$event',[]],['form']]]]]}}" value="{{form.work_time}}" bindinput="__e" class="data-v-6a3401a4"/></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>选择区域</view><input type="text" placeholder="请选择代理区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e3',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e" class="data-v-6a3401a4"/></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>详细地址</view><input type="text" placeholder="请输入详细地址" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['form']]]]]}}" value="{{form.address}}" bindinput="__e" class="data-v-6a3401a4"/></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>自我介绍</view><input type="text" placeholder="请输入自我介绍" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['form']]]]]}}" value="{{form.text}}" bindinput="__e" class="data-v-6a3401a4"/></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>身份证号</view><input type="text" placeholder="请输入身份证号" data-event-opts="{{[['input',[['__set_model',['$0','id_code','$event',[]],['form']]]]]}}" value="{{form.id_code}}" bindinput="__e" class="data-v-6a3401a4"/></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>上传身份证照片</view><view class="card data-v-6a3401a4"><view class="card_item data-v-6a3401a4"><view class="top data-v-6a3401a4"><view class="das data-v-6a3401a4"><view class="up data-v-6a3401a4"><upload vue-id="4766983c-8" imagelist="{{form.id_card1}}" imgtype="id_card1" imgclass="id_card_box" text="身份证人像面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^changeShow',[['changeShow']]]]}}" bind:upload="__e" bind:changeShow="__e" class="data-v-6a3401a4" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-6a3401a4">拍摄人像面</view></view><view class="card_item data-v-6a3401a4"><view class="top data-v-6a3401a4"><view class="das data-v-6a3401a4"><view class="up data-v-6a3401a4"><upload vue-id="4766983c-9" imagelist="{{form.id_card2}}" imgtype="id_card2" imgclass="id_card_box" text="身份证国徽面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^changeShow',[['changeShow']]]]}}" bind:upload="__e" bind:changeShow="__e" class="data-v-6a3401a4" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-6a3401a4">拍摄国徽面</view></view></view></view><view class="main_item data-v-6a3401a4"><view class="title data-v-6a3401a4"><label class="_span data-v-6a3401a4">*</label>上传形象照片</view><upload vue-id="4766983c-10" imagelist="{{form.self_img}}" imgtype="self_img" imgclass text="形象照片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]],['^changeShow',[['changeShow']]]]}}" bind:upload="__e" bind:del="__e" bind:changeShow="__e" class="data-v-6a3401a4" bind:__l="__l"></upload></view></view><block wx:if="{{!shInfo.status}}"><view class="footer data-v-6a3401a4"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="btn data-v-6a3401a4" bindtap="__e">立即提交</view></view></block></view>
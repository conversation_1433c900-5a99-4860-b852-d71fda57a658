@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-1189852d {
  height: 100vh;
  background-color: #f3f4f5;
  overflow: auto;
  padding-top: 40rpx;
}
.page .box.data-v-1189852d {
  margin: 0 auto;
  width: 690rpx;
  height: 1072rpx;
  background: #FFFFFF;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  padding: 40rpx 24rpx;
}
.page .box .title.data-v-1189852d {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .box .infoBox.data-v-1189852d {
  margin-top: 40rpx;
}
.page .box .infoBox .info_item.data-v-1189852d {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.page .box .infoBox .info_item .left.data-v-1189852d {
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}
.page .box .infoBox .info_item .right.data-v-1189852d {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .box .imgbox.data-v-1189852d {
  display: flex;
  margin-top: 40rpx;
  margin-bottom: 20rpx;
}
.page .box .imgbox image.data-v-1189852d {
  width: 196rpx;
  height: 196rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}
.page .box .notes.data-v-1189852d {
  margin-top: 36rpx;
  width: 628rpx;
  min-height: 242rpx;
  background: #F7F7F7;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  padding: 40rpx 30rpx;
}

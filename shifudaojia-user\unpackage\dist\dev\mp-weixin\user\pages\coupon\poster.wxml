<view><view class="hideCanvasView"><wxml-to-canvas class="hideCanvas vue-ref" vue-id="7a761d91-1" width="{{652}}" height="{{964}}" data-ref="canvas" bind:__l="__l"></wxml-to-canvas></view><block wx:if="{{src}}"><block><image class="code-img" src="{{src}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="pd-lg"><button data-event-opts="{{[['tap',[['saveImage',['$event']]]]]}}" class="save-btn flex-center radius" style="{{'background:'+(primaryColor)+';'}}" bindtap="__e">保存图片至相册</button></view></block></block></view>
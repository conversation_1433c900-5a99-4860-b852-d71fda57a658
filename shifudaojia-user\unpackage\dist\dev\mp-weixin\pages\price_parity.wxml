<view class="page data-v-53c348c8"><view class="header data-v-53c348c8"><image src="{{serviceInfo.cover}}" mode="scaleToFill" class="data-v-53c348c8"></image></view><view class="card data-v-53c348c8"><view class="top data-v-53c348c8"><view class="title data-v-53c348c8">{{serviceInfo.title}}</view><block wx:if="{{serviceInfo.service_price_type!=1}}"><view class="price data-v-53c348c8">{{"￥"+serviceInfo.price}}</view></block></view><view class="bottom data-v-53c348c8"><view class="left data-v-53c348c8">已选：</view><view class="right data-v-53c348c8"><block wx:for="{{chooseArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tag data-v-53c348c8">{{item.name}}</view></block></view></view></view><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-53c348c8"><view class="choose data-v-53c348c8"><view class="title data-v-53c348c8"><block wx:if="{{item.is_required==1}}"><label class="_span data-v-53c348c8">*</label></block>{{item.problem_desc}}</view><view class="desc data-v-53c348c8">{{item.problem_content}}</view><view class="cho_box data-v-53c348c8"><block wx:for="{{item.options}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view data-event-opts="{{[['tap',[['chooseOne',[index,newIndex,'$0'],[[['list','',index,'input_type']]]]]]]}}" class="box_item data-v-53c348c8" style="{{(newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':'')}}" bindtap="__e">{{''+newItem.name+''}}<view class="ok data-v-53c348c8" style="{{(newItem.choose?'':'display:none;')}}"><uni-icons vue-id="{{'24a4ce2c-1-'+index+'-'+newIndex}}" type="checkmarkempty" size="8" color="#fff" class="data-v-53c348c8" bind:__l="__l"></uni-icons></view></view></block></view></view><view class="fg data-v-53c348c8"></view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-53c348c8"><view class="choose data-v-53c348c8"><view class="title data-v-53c348c8"><block wx:if="{{item.$orig.is_required==1}}"><label class="_span data-v-53c348c8">*</label></block>{{item.$orig.problem_desc}}</view><view class="desc data-v-53c348c8">{{item.$orig.problem_content}}</view><input type="text" data-event-opts="{{[['input',[['__set_model',['$0','val','$event',[]],['form.data.'+item.g1+'']]]]]}}" value="{{form.data[item.g2].val}}" bindinput="__e" class="data-v-53c348c8"/></view><view class="fg data-v-53c348c8"></view></view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-53c348c8"><view class="choose data-v-53c348c8"><view class="title data-v-53c348c8"><block wx:if="{{item.$orig.is_required==1}}"><label class="_span data-v-53c348c8">*</label></block>{{item.$orig.problem_desc}}</view><view class="desc up data-v-53c348c8">{{item.$orig.problem_content}}</view><upload vue-id="{{'24a4ce2c-2-'+index}}" imagelist="{{form.data[item.g3].val}}" imgtype="{{item.g4}}" text="上传图片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-53c348c8" bind:__l="__l"></upload></view><view class="fg data-v-53c348c8"></view></view></block><view class="footer data-v-53c348c8"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="btn data-v-53c348c8" bindtap="__e">立即下单</view></view></view>
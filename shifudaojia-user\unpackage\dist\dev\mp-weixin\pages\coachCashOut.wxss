@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-79ec8ccc {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding: 20rpx 0;
}
.page .header.data-v-79ec8ccc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  font-weight: 500;
  color: #3B3B3B;
  padding: 0 30rpx;
  width: 750rpx;
  height: 118rpx;
  background: #FFFFFF;
}
.page .header .right.data-v-79ec8ccc {
  display: flex;
  align-items: center;
}
.page .mid.data-v-79ec8ccc {
  margin-top: 20rpx;
  width: 750rpx;
  height: 276rpx;
  background: #FFFFFF;
  padding: 0 30rpx;
  padding-top: 40rpx;
}
.page .mid .title.data-v-79ec8ccc {
  font-size: 28rpx;
  font-weight: 500;
  color: #3B3B3B;
}
.page .mid .top.data-v-79ec8ccc {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding-top: 28rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #F2F3F6;
}
.page .mid .top .r_left.data-v-79ec8ccc {
  font-size: 28rpx;
  font-weight: 500;
  color: #E51837;
}
.page .mid .bottom.data-v-79ec8ccc {
  padding-top: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
}
.page .btn.data-v-79ec8ccc {
  margin: 0 auto;
  margin-top: 60rpx;
  width: 690rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}
.page .tips.data-v-79ec8ccc {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
  text-align: center;
  margin-top: 20rpx;
}

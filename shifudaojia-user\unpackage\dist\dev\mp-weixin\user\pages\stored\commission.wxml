<view class="order-pages"><fixed vue-id="45fe3a30-1" bind:__l="__l" vue-slots="{{['default']}}"><view class="flex-center flex-column pd-lg c-base" style="{{'background:'+(primaryColor)+';'}}"><view class="flex-y-baseline f-title">¥<view class="money">{{list.total_cash||0}}</view></view><view class="f-paragraph">总收益</view></view><tab vue-id="{{('45fe3a30-2')+','+('45fe3a30-1')}}" list="{{tabList}}" activeIndex="{{activeIndex*1}}" activeColor="{{primaryColor}}" width="{{100/$root.g0+'%'}}" height="100rpx" data-event-opts="{{[['^change',[['handerTabChange']]]]}}" bind:change="__e" bind:__l="__l"></tab><view class="b-1px-b"></view></fixed><block wx:for="{{list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',[index,'detail']]]]]}}" class="item-child mt-md ml-lg mr-lg pd-lg fill-base radius-16" catchtap="__e"><view data-event-opts="{{[['tap',[['goShop',[index]]]]]}}" class="flex-between pb-lg" catchtap="__e"><view class="f-paragraph c-title max-380 ellipsis">{{"下单人："+(item.nickName||'')}}</view><view class="f-caption text-bold" style="{{'color:'+(item.status==1?primaryColor:'#333')+';'}}">{{''+statusType[item.status]+''}}</view></view><block wx:for="{{item.order_goods}}" wx:for-item="aitem" wx:for-index="aindex" wx:key="aindex"><view class="flex-center mb-lg"><image class="avatar lg radius-16" mode="aspectFill" src="{{aitem.goods_cover}}"></image><view class="flex-1 ml-md"><view class="flex-between"><view class="goods-title f-title c-title ellipsis">{{''+aitem.goods_name+''}}</view></view><view class="f-caption c-caption mt-sm mb-sm">{{"服务技师："+(item.coach_name||'')}}</view><view class="flex-between"><view class="flex-y-baseline f-caption c-warning">¥<view class="f-title text-bold">{{''+aitem.true_price+''}}</view></view><view class="c-paragraph">{{"x"+aitem.num}}</view></view></view></view></block><view class="flex-between pt-lg b-1px-t"><view class="flex-y-center f-desc c-title">付款：<view class="text-bold">{{"¥"+item.pay_price}}</view></view><view class="flex-y-center f-desc c-title">分销佣金：<view class="c-warning text-bold">{{"¥"+item.cash}}</view></view></view></view></block><block wx:if="{{loading}}"><load-more vue-id="45fe3a30-3" noMore="{{$root.g1}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g2}}"><abnor vue-id="45fe3a30-4" bind:__l="__l"></abnor></block><view class="space-footer"></view><common-popup class="vue-ref" vue-id="45fe3a30-5" type="CANCEL_ORDER" info="{{popupInfo}}" data-ref="cancel_item" data-event-opts="{{[['^confirm',[['confirmCancel']]]]}}" bind:confirm="__e" bind:__l="__l"></common-popup><common-popup class="vue-ref" vue-id="45fe3a30-6" type="DELETE_ORDER" info="{{popupInfo}}" data-ref="del_item" data-event-opts="{{[['^confirm',[['confirmDel']]]]}}" bind:confirm="__e" bind:__l="__l"></common-popup></view>
<view class="pages-mine"><yk-authpup class="vue-ref" vue-id="fe5e5c8c-1" type="top" permissionID="{{permissionID}}" data-ref="authpup" data-event-opts="{{[['^changeAuth',[['changeAuth']]]]}}" bind:changeAuth="__e" bind:__l="__l"></yk-authpup><view class="pd-lg" style="height:292rpx;background-color:#599EFF;"><view class="{{['pt-lg',[(userInfo.nickName)?'flex-warp':''],[(!userInfo.nickName)?'flex-center':'']]}}"><view class="avatar_view"><image class="avatar radius" mode="aspectFill" src="{{mineInfo.avatarUrl||'/static/mine/default_user.png'}}"></image><block wx:if="{{mineInfo.is_admin==1}}"><view class="text">代理商</view></block><block wx:if="{{mineInfo.coach_status==2}}"><view style="position:absolute;right:-10rpx;top:-10rpx;"><u-icon vue-id="fe5e5c8c-2" name="integral-fill" color="#ffc82e" size="24" bind:__l="__l"></u-icon></view></block></view><block wx:if="{{userInfo&&(!userInfo.phone||!userInfo.nickName)}}"><block><auth class="flex-1" vue-id="fe5e5c8c-3" needAuth="{{userInfo&&(!userInfo.phone||!userInfo.nickName)}}" must="{{true}}" type="{{!userInfo.phone?'phone':'userInfo'}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="flex-1 f-md-title text-bold ml-md" style="{{'color:'+(configInfo[font_type[userPageType]])+';'}}">{{(userInfo.nickName?'绑定手机号':'用户')+''}}</view></auth></block></block><block wx:else><view class="flex-1 ml-md mt-sm rel" style="{{'color:'+(configInfo[font_type[userPageType]])+';'}}"><view class="flex-between"><view data-event-opts="{{[['tap',[['authUserProfile',['$event']]]]]}}" class="flex-y-center f-title text-bold" catchtap="__e"><view class="mr-sm max-500 ellipsis" style="color:#fff;">{{''+(userInfo.nickName?userInfo.nickName:'去登录')+''}}</view><block wx:if="{{userPageType==2&&coach_info.label_name}}"><view style="width:fit-content;padding:0 10rpx;font-size:20rpx;background-color:#16d46b;border-radius:5rpx;">{{''+coach_info.label_name+''}}</view></block></view></view><block wx:if="{{userInfo.phone}}"><view class="flex-between"><view class="member-tag flex-center mt-sm pl-md pr-md f-caption radius">{{''+userInfo.phone+''}}</view></view></block><block wx:if="{{mineInfo.coach_status==2}}"><view data-event-opts="{{[['tap',[['toChooseLocation',['$event']]]]]}}" class="flex-x-center mt-md f-caption" style="height:72rpx;" catchtap="__e"><view class="iconfont iconjuli mr-sm _i"></view><view class="flex-1 ellipsis-2">{{coach_info.address}}</view></view></block><block wx:else><view class="mt-md" style="height:72rpx;"></view></block></view></block><view data-event-opts="{{[['tap',[['goSet',['$event']]]]]}}" class="notice-item ml-md" catchtap="__e"><view class="iconfont icon-xitong text-bold _i" style="color:#fff;"></view></view></view></view><block wx:if="{{userPageType==1}}"><block><view class="mine-menu-list box-shadow fill-base box1"><view class="menu-title flex-between pl-lg pr-md b-1px-b"><view class="f-paragraph c-title text-bold">我的订单</view></view><view class="flex-warp pt-lg pb-lg"><block wx:for="{{orderList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><auth style="width:25%;" vue-id="{{'fe5e5c8c-4-'+index}}" needAuth="{{userInfo&&(!userInfo.phone||!userInfo.nickName)}}" must="{{true}}" type="{{!userInfo.phone?'phone':'userInfo'}}" data-event-opts="{{[['^go',[['e0']]]]}}" data-event-params="{{({item})}}" bind:go="__e" bind:__l="__l" vue-slots="{{['default']}}"><view style="display:flex;flex-direction:column;align-items:center;"><image style="width:46rpx;height:50rpx;" src="{{item.icon}}" mode></image><view class="mt-sm">{{item.text}}</view></view></auth></block></view></view><view style="height:40rpx;background-color:#F8F8F8;"></view><view class="mine-tool-list fill-base"><block wx:for="{{toolList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block><auth vue-id="{{'fe5e5c8c-5-'+index}}" needAuth="{{userInfo&&(!userInfo.phone||!userInfo.nickName)}}" must="{{true}}" type="{{!userInfo.phone?'phone':'userInfo'}}" data-event-opts="{{[['^go',[['gogo',['$0'],[[['toolList','',index,'url']]]]]]]}}" bind:go="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['list-item','pt-lg','pb-lg','ml-lg','mr-lg','flex-center',[(index!=0)?'b-1px-t':'']]}}"><u-icon vue-id="{{('fe5e5c8c-6-'+index)+','+('fe5e5c8c-5-'+index)}}" name="{{item.icon}}" color="#599eff" size="24" bind:__l="__l"></u-icon><view class="flex-1 flex-between ml-md"><view class="f-paragraph c-title">{{item.text}}</view><view class="{{['iconfont','_i',[(item.text=='联系客服')?'iconbodadianhua text-bold':''],[(item.text!='联系客服')?'icon-right':'']]}}" style="{{'font-size:'+(item.text=='联系客服'?'50rpx':'')+';'+('color:'+(item.text=='联系客服'?primaryColor:'')+';')}}"></view></view></view></auth></block></block></block></view></block></block><block wx:if="{{userPageType==2}}"><block><view class="mine-menu-list box-shadow fill-base" style="border-radius:16px 16px 0 0;"><view class="menu-title flex-between pl-lg pr-sm b-1px-b"><view class="f-paragraph c-title text-bold">我的订单</view></view><view class="flex-warp"><block wx:for="{{orderList2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goUrl',['$0'],[[['orderList2','',index,'url']]]]]]]}}" class="item-child flex-center flex-column f-caption c-paragraph" style="width:33.3%;" catchtap="__e"><view class="item-img rel flex-center radius"><view class="item-img radius abs" style="{{'background:'+('#fff')+';'}}"></view><view class="{{['iconfont','c-title','_i',item.icon]}}" style="{{'color:'+('#3986fd')+';'}}"></view></view><view class="mt-sm">{{item.text}}</view></view></block></view></view><view class="mine-menu-list box-shadow fill-base"><view class="menu-title flex-between pl-lg pr-sm b-1px-b"><view class="f-paragraph c-title text-bold">常用功能</view></view><view class="flex-warp"><block wx:for="{{orderList3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goUrl',['$0'],[[['orderList3','',index,'url']]]]]]]}}" class="item-child flex-center flex-column f-caption c-paragraph" style="width:33.3%;" catchtap="__e"><view class="item-img rel flex-center radius"><view class="item-img radius abs" style="{{'background:'+('#fff')+';'}}"></view><view class="{{['iconfont','c-title','_i',item.icon]}}" style="{{'color:'+('#3986fd')+';'}}"></view></view><view class="mt-sm">{{item.text}}</view></view></block></view></view><view class="mine-menu-list box-shadow fill-base"><view class="menu-title flex-between pl-lg pr-sm b-1px-b"><view class="f-paragraph c-title text-bold">其他功能</view></view><view class="flex-warp"><block wx:for="{{orderList4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goUrl',['$0'],[[['orderList4','',index,'url']]]]]]]}}" class="item-child flex-center flex-column f-caption c-paragraph" style="width:33.3%;" catchtap="__e"><view class="item-img rel flex-center radius"><view class="item-img radius abs" style="{{'background:'+('#fff')+';'}}"></view><view class="{{['iconfont','c-title','_i',item.icon]}}" style="{{'color:'+('#3986fd')+';'}}"></view></view><view class="mt-sm">{{item.text}}</view></view></block></view></view><view style="height:20px;background-color:#f8f8f8;"></view><view class="mine-tool-list box-shadow fill-base"><block wx:for="{{toolList2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goUrl',['$0'],[[['toolList2','',index,'url']]]]]]]}}" class="{{['list-item','pt-lg','pb-lg','ml-lg','mr-lg','flex-center',[(index!=0)?'b-1px-t':'']]}}" catchtap="__e"><view class="{{['iconfont','_i',item.icon]}}" style="{{'color:'+('#3986fd')+';'}}"></view><view class="flex-1 flex-between ml-md"><view class="f-paragraph c-title">{{item.text}}</view><block wx:if="{{item.url=='change'}}"><block><view class="iconfont icon-switch c-caption _i"></view></block></block><block wx:else><view class="iconfont icon-right _i"></view></block></view></view></block></view></block></block><tabbar vue-id="fe5e5c8c-7" cur="{{3}}" bind:__l="__l"></tabbar></view>
<view class="page data-v-cf927e3e"><u-modal vue-id="6ea1dc45-1" show="{{show}}" content="{{content}}" showCancelButton="{{true}}" cancelText="再想想" data-event-opts="{{[['^cancel',[['e0']]],['^confirm',[['confirmMaster']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-cf927e3e" bind:__l="__l"></u-modal><view class="header data-v-cf927e3e"><view class="title data-v-cf927e3e">等待您选择师傅</view><view class="desc data-v-cf927e3e">师傅可能在服务，请耐心等待</view><view class="time data-v-cf927e3e">距师傅报价截止还剩：<u-count-down vue-id="6ea1dc45-2" time="{{24*60*60*1000}}" format="HH:mm:ss" class="data-v-cf927e3e" bind:__l="__l"></u-count-down></view></view><view class="main data-v-cf927e3e"><scroll-view scroll-y="true" class="data-v-cf927e3e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="main_item data-v-cf927e3e"><view class="time data-v-cf927e3e">{{"报价时间"+item.g0}}</view><view class="box data-v-cf927e3e"><image src="{{item.$orig.self_img}}" mode class="data-v-cf927e3e"></image><view class="mid data-v-cf927e3e"><view class="top data-v-cf927e3e"><view class="name data-v-cf927e3e">{{item.$orig.coach_name}}</view><block wx:if="{{item.$orig.label_name!=null}}"><view class="level data-v-cf927e3e">{{item.$orig.label_name}}</view></block><block wx:if="{{item.$orig.cash_pledge!=null}}"><view class="promise data-v-cf927e3e">已缴纳保证金</view></block></view><view class="bottom data-v-cf927e3e">服务<label class="_span data-v-cf927e3e">{{item.$orig.count}}</label>次</view></view><view class="price data-v-cf927e3e">{{"￥"+item.$orig.price}}</view></view><view class="down data-v-cf927e3e"><view data-event-opts="{{[['tap',[['chooseOne',['$0'],[[['info.quoted_price','',index]]]]]]]}}" class="btn data-v-cf927e3e" bindtap="__e">选择他</view></view></view></block></scroll-view></view><view class="footer data-v-cf927e3e"><view data-event-opts="{{[['tap',[['cancelO',['$event']]]]]}}" class="btn data-v-cf927e3e" bindtap="__e">取消订单</view></view><u-modal vue-id="6ea1dc45-3" show="{{showCancel}}" title="取消订单" content="确认要取消该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e1']]],['^confirm',[['confirmCancel']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-cf927e3e" bind:__l="__l"></u-modal></view>
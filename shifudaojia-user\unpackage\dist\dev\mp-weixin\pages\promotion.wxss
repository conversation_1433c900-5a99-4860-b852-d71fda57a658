@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-77dd720c {
  height: 100vh;
  background-color: #f8f8f8;
}
.page .header.data-v-77dd720c {
  width: 750rpx;
  height: 410rpx;
  background: linear-gradient(to bottom, #599eff, #f8f8f8);
  position: absolute;
  top: 0;
  padding: 40rpx 30rpx;
}
.page .header .info.data-v-77dd720c {
  display: flex;
  align-items: center;
}
.page .header .info image.data-v-77dd720c {
  width: 122rpx;
  height: 122rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.page .header .info .name.data-v-77dd720c {
  font-size: 32rpx;
  font-weight: 600;
  color: #FFFFFF;
}
.page .money.data-v-77dd720c {
  width: 690rpx;
  height: 342rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  position: absolute;
  left: 30rpx;
  top: 182rpx;
  padding: 40rpx;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
}
.page .money .money_item.data-v-77dd720c {
  width: 50%;
  height: 50%;
  box-sizing: border-box;
}
.page .money .money_item .title.data-v-77dd720c {
  font-size: 24rpx;
  font-weight: 400;
  color: #3B3B3B;
}
.page .money .money_item .num.data-v-77dd720c {
  margin-top: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #3B3B3B;
}
.page .money .money_item .btn.data-v-77dd720c {
  width: 188rpx;
  height: 76rpx;
  background: #2E80FE;
  border-radius: 38rpx 38rpx 38rpx 38rpx;
  line-height: 76rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
}
.page .money .x1.data-v-77dd720c {
  border-bottom: 2rpx solid #F2F3F6;
}
.page .money .x2.data-v-77dd720c {
  border-bottom: 2rpx solid #F2F3F6;
  border-left: 2rpx solid #F2F3F6;
  padding-left: 32rpx;
}
.page .money .x4.data-v-77dd720c {
  border-left: 2rpx solid #F2F3F6;
  padding-left: 32rpx;
  padding-top: 40rpx;
}
.page .money .x3.data-v-77dd720c {
  padding-top: 40rpx;
}
.page .box.data-v-77dd720c {
  width: 690rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 40rpx;
  position: absolute;
  left: 30rpx;
  top: 564rpx;
}
.page .box .box_item.data-v-77dd720c {
  width: 332rpx;
  height: 234rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin-bottom: 20rpx;
  padding: 54rpx 0;
}
.page .box .box_item image.data-v-77dd720c {
  margin: 0 auto;
  width: 63rpx;
  height: 65rpx;
}
.page .box .box_item text.data-v-77dd720c {
  margin-top: 20rpx;
  display: block;
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #3B3B3B;
}

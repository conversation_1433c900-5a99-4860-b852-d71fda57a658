<template>
	<view class="user-pages-info" v-if="isLoad">
		<view class="pd-lg f-paragraph fill-base">
			<parser :html="detail[options.key]" @linkpress="linkpress" show-with-animation lazy-load>加载中...
			</parser>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions,
		mapMutations
	} from "vuex"
	import parser from "@/components/jyf-Parser/index"
	export default {
		components: {
			parser
		},
		data() {
			return {
				options: {},
				isLoad: false,
				detail: {},
				title: {
					1: '用户隐私协议',
					2: '个人信息保护指引'
				}
			}
		},
		computed: mapState({
			primaryColor: state => state.config.configInfo.primaryColor,
			subColor: state => state.config.configInfo.subColor,
			userInfo: state => state.user.userInfo,
		}),
		onLoad(options) {
			let {
				type = 1
			} = options
			options.key = type == 1 ? 'login_protocol' : 'information_protection'
			this.options = options
			this.$util.showLoading()
			this.initIndex()
			uni.setNavigationBarTitle({
				title: this.title[type]
			})
		},
		methods: {
			...mapActions(['getConfigInfo', 'getUserInfo']),
			...mapMutations(['getUserIndex']),
			async initIndex(refresh = false) {
				// #ifdef H5
				if (!refresh && this.$jweixin.isWechat()) {
					await this.$jweixin.initJssdk();
					this.$jweixin.wxReady(() => {
						this.$jweixin.hideOptionMenu()
					})
				}
				// #endif
				let {
					type
				} = this.options
				this.detail = await this.$api.base.getConfig()
				this.$util.setNavigationBarColor({
					bg: this.primaryColor
				})
				this.$util.hideAll()
				this.isLoad = true
			},
			initRefresh() {
				this.initIndex(true)
			},
			linkpress(res) {
				console.log("linkpress", res);
				// #ifdef APP-PLUS
				this.$util.goUrl({
					url: res.href,
					openType: 'web'
				})
				// #endif
			},
		}
	}
</script>


<style lang="scss">
	.pages-home {
		.list-item {
			.item-btn {
				width: 129rpx;
				height: 54rpx;
			}
		}
	}
</style>

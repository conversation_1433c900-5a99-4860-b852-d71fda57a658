<view><scroll-view class="scroll-left fill-base" scroll-y="{{true}}" scroll-into-view="{{scrollNav}}" scroll-with-animation="{{true}}"><block wx:for="{{storeDay}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item-child','flex-center','f-paragraph','c-title',[(index==scrollInd)?'active':'']]}}" style="{{'color:'+(index==scrollInd?primaryColor:'')+';'}}" id="{{'scrollNav'+index}}" data-event-opts="{{[['tap',[['onChangeNav',[index,1]]]]]}}" bindtap="__e"><view class="flex-center child ellipsis" style="{{'border-left:'+(index==scrollInd?'5rpx solid '+primaryColor:'')+';'}}">{{''+(item.dat_text+' ('+item.week+')')+''}}</view></view></block></block></scroll-view><view class="scroll-right abs fill-base pl-lg"><block wx:for="{{storeTime}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onChangeNav',[index,2]]]]]}}" class="item-child flex-center f-paragraph c-paragraph" style="{{'padding-right:25rpx;'+('color:'+(index==checkInd?primaryColor:'')+';')}}" bindtap="__e"><view class="{{['flex-1',[(item.status!=1)?'text-delete':'']]}}">{{item.time_text}}</view><block wx:if="{{index==checkInd}}"><view class="iconfont icon-xuanze-fill _i"></view></block></view></block><view class="mg-lg"><block wx:if="{{$root.g0}}"><abnor vue-id="677a0e84-1" bind:__l="__l"></abnor></block></view></view></view>
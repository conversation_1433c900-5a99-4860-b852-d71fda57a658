<view data-ref="limepainter" class="lime-painter vue-ref"><block wx:if="{{canvasId&&size}}"><view style="{{(styles)}}"><block wx:if="{{use2dCanvas}}"><canvas class="lime-painter__canvas" style="{{(size)}}" id="{{canvasId}}" type="2d"></canvas></block><block wx:else><canvas class="lime-painter__canvas" style="{{(size)}}" canvas-id="{{canvasId}}" id="{{canvasId}}" width="{{boardWidth*dpr}}" height="{{boardHeight*dpr}}"></canvas></block></view></block><slot></slot></view>
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page .header.data-v-791cbe48 {
  padding: 40rpx 30rpx;
}
.page .header .head.data-v-791cbe48 {
  width: 690rpx;
  height: 186rpx;
  background: #2E80FE;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}
.page .header .head .left.data-v-791cbe48 {
  display: flex;
  align-items: center;
}
.page .header .head .left image.data-v-791cbe48 {
  width: 106rpx;
  height: 106rpx;
  border-radius: 50%;
}
.page .header .head .left .name.data-v-791cbe48 {
  margin-left: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  max-width: 240rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .header .head .right.data-v-791cbe48 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 74rpx;
  background: #81B3FF;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  line-height: 74rpx;
  text-align: center;
  padding: 0 14rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}
.page .fg.data-v-791cbe48 {
  background: #f3f4f5;
  width: 100%;
  height: 20rpx;
}
.page .box.data-v-791cbe48 {
  padding: 40rpx 30rpx;
}
.page .box .title.data-v-791cbe48 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .box .list.data-v-791cbe48 {
  margin-top: 42rpx;
}
.page .box .list .list_item.data-v-791cbe48 {
  display: flex;
  margin-bottom: 20rpx;
}
.page .box .list .list_item image.data-v-791cbe48 {
  width: 104rpx;
  height: 104rpx;
  border-radius: 50%;
}
.page .box .list .list_item .info.data-v-791cbe48 {
  margin-left: 20rpx;
}
.page .box .list .list_item .info .nam.data-v-791cbe48 {
  font-size: 28rpx;
  font-weight: 400;
  color: #171717;
  max-width: 480rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .box .list .list_item .info .phone.data-v-791cbe48 {
  margin-top: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}

<block wx:if="{{orderInfo.coach_id}}"><view class="order-pages"><block wx:if="{{$root.g0}}"><view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="address-info flex-warp mt-lg ml-lg mr-lg pd-lg fill-base radius-16" catchtap="__e"><view class="address-icon flex-center c-base radius" style="{{'background:'+('linear-gradient(to right, '+subColor+', '+primaryColor+')')+';'}}"><view class="iconfont iconjuli _i"></view></view><view class="flex-1 flex-between ml-md"><view class="max-500"><block wx:if="{{orderInfo.address_info.id}}"><block><view class="flex-y-baseline username c-title text-bold">{{''+orderInfo.address_info.user_name+''}}<view class="ml-md f-desc c-caption">{{orderInfo.address_info.mobile}}</view></view><view class="f-desc c-title ellipsis">{{''+(orderInfo.address_info.address+' '+orderInfo.address_info.address_info)+''}}</view></block></block><block wx:else><block><view class="username c-title text-bold">请选择地址</view></block></block></view><view class="iconfont icon-right _i"></view></view></view><view class="mt-md ml-lg mr-lg fill-base radius-16"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="flex-between pt-lg pb-lg pl-lg pr-md b-1px-b" catchtap="__e"><view class="f-title c-title text-bold">服务时间</view><view class="flex-y-center f-paragraph c-caption ml-sm"><view class="c-caption mr-sm">{{send_info.time.time||'请选择预约时间'}}</view><view class="iconfont icon-right _i"></view></view></view><view class="flex-between pd-lg"><view class="f-title c-title text-bold">出行方式</view><view class="flex-center"><block wx:for="{{carTypeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toChangeItem',[index]]]]]}}" hidden="{{!(item.id==1||isBus==1&&item.id==0)}}" class="{{['flex-y-center',[(index==0)?'mr-lg':'']]}}" style="{{'color:'+(carTypeInd==index?primaryColor:'')+';'}}" catchtap="__e"><view class="{{['iconfont','icon-xuanze','mr-sm','_i',[(carTypeInd==index)?'icon-xuanze-fill':'']]}}"></view>{{item.title+''}}</view></block></view></view></view><view class="mt-md ml-lg mr-lg fill-base radius-16"><block wx:for="{{orderInfo.order_goods}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['list-item','flex-center','pd-lg',[(index!=0)?'b-1px-t':'']]}}"><image class="item-img radius-16" mode="aspectFill" src="{{item.cover}}"></image><view class="flex-1 ml-md"><view class="f-title c-title ellipsis">{{item.title}}</view><view class="flex-y-center f-desc c-caption"><view class="f-title c-warning mr-sm">{{"¥"+item.price}}</view>{{"/ "+item.time_long+'分钟'}}</view><view class="flex-between"><view class="f-caption c-caption mt-sm mb-sm max-300">{{'服务技师：'+orderInfo.coach_info.coach_name+''}}</view><view class="flex-warp"><button data-event-opts="{{[['tap',[['changeNum',[-1,index]]]]]}}" class="reduce" style="{{'border-color:'+(primaryColor)+';'+('color:'+(primaryColor)+';')}}" catchtap="__e"><view class="iconfont icon-jian-bold _i"></view></button><button class="addreduce clear-btn">{{item.num||0}}</button><button data-event-opts="{{[['tap',[['changeNum',[1,index]]]]]}}" class="add" style="{{'background:'+(primaryColor)+';'+('border-color:'+(primaryColor)+';')}}" catchtap="__e"><view class="iconfont icon-jia-bold _i"></view></button></view></view></view></view></block></view><view class="mt-md ml-lg mr-lg fill-base radius-16"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['flex-between','pt-lg','pb-lg','pl-lg','pr-md',[(carTypeList[carTypeInd].id===1)?'b-1px-b':'']]}}" catchtap="__e"><view class="f-title c-title text-bold">卡券优惠</view><view class="flex-y-center f-paragraph c-caption ml-sm"><view class="c-warning mr-sm">{{''+(orderInfo.discount?'-¥'+orderInfo.discount:orderInfo.canUseCoupon+'张可用')+''}}</view><view class="iconfont icon-right _i"></view></view></view><block wx:if="{{carTypeList[carTypeInd].id===1}}"><block><view class="flex-between pd-lg"><view class="f-title c-title text-bold">往返车费</view><view class="f-paragraph c-caption c-warning">{{"¥"+orderInfo.car_price}}</view></view><view class="pl-lg pr-lg pb-lg f-caption c-caption">{{'全程共'+orderInfo.distance+"，出租出行"+orderInfo.car_config.start_distance+"公里内，起步"+orderInfo.car_config.start_price+"元。里程计价："+orderInfo.car_config.distance_price+'元/公里'}}</view></block></block></view><view class="mt-md ml-lg mr-lg pd-lg fill-base radius-16"><view class="flex-between pb-lg"><view class="flex-y-baseline"><view class="f-title c-title text-bold">订单备注</view><view class="f-paragraph c-caption ml-sm">(选填)</view></view></view><view class="f-caption c-caption fill-body radius-16"><textarea class="item-textarea f-paragraph pd-lg" placeholder-class="f-paragraph" maxlength="100" placeholder="输入订单备注" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['form']]]]]}}" value="{{form.text}}" bindinput="__e"></textarea><view class="text-right pb-lg pr-lg">{{($root.g1>100?100:$root.g2)+'/100'}}</view></view></view><view class="mt-md ml-lg mr-lg fill-base radius-16"><block wx:for="{{payList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toChangeItem',[index,2]]]]]}}" class="flex-between pt-lg pb-lg pl-lg pr-md b-1px-b" catchtap="__e"><view class="flex-y-center f-title c-title"><view class="{{['iconfont','mr-md','_i',item.icon]}}" style="{{'color:'+(item.id==1?primaryColor:'')+';'+('font-size:'+('50rpx')+';')}}"></view>{{''+item.title+''}}<block wx:if="{{item.id==1}}"><view class="f-paragraph c-caption ml-md">{{"余额"+(balance||0)+"元"}}</view></block></view><view class="flex-y-center" style="{{'color:'+(payInd==index?primaryColor:'')+';'}}"><view class="{{['iconfont','icon-xuanze','mr-sm','_i',[(payInd==index)?'icon-xuanze-fill':'']]}}"></view></view></view></block></view><block wx:if="{{$root.g3}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="mt-md ml-lg mr-lg pd-lg fill-base f-paragraph c-title flex-y-center radius-16" catchtap="__e"><view class="{{['iconfont','mr-sm','_i',isAgree?'icon-xuanze-fill':'icon-xuanze']}}" style="{{'color:'+(isAgree?primaryColor:'')+';'}}"></view>我已阅读并同意<view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" style="{{'color:'+(primaryColor)+';'}}" catchtap="__e">《平台交易规则》</view></view></block><view class="space-max-footer"></view><view class="pay-info fix flex-between text-right pl-lg pr-lg fill-base"><view class="flex-y-center f-paragraph c-title text-bold ml-sm mr-lg">合计：<view class="flex-y-baseline f-title c-warning">{{"¥"+orderInfo.pay_price}}</view></view><auth style="width:182rpx;" vue-id="00096cce-1" needAuth="{{userInfo&&(!userInfo.phone||!userInfo.nickName)}}" must="{{true}}" type="{{!userInfo.phone?'phone':'userInfo'}}" data-event-opts="{{[['^go',[['toPay']]]]}}" bind:go="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="pay-btn flex-center f-paragraph c-base radius" style="{{'background:'+(primaryColor)+';'}}">立即支付</view></auth></view></view></block><block wx:else><abnor vue-id="00096cce-2" tip="{{[{text:'该服务已下架~',color:0}]}}" button="{{[{text:'去看看其他服务',type:'confirm'}]}}" btnSize data-event-opts="{{[['^confirm',[['e5']]]]}}" bind:confirm="__e" bind:__l="__l"></abnor></block><uni-popup class="vue-ref" vue-id="00096cce-3" type="center" maskClick="{{false}}" data-ref="show_rule_item" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-rule"><view class="fill-base pd-lg radius-26"><view class="f-title c-title text-bold flex-center pd-lg">平台交易规则</view><scroll-view class="rule-text" scroll-y="{{true}}" data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" catchtouchmove="__e"><parser vue-id="{{('00096cce-4')+','+('00096cce-3')}}" html="{{configInfo.trading_rules}}" show-with-animation="{{true}}" lazy-load="{{true}}" data-event-opts="{{[['^linkpress',[['linkpress']]]]}}" bind:linkpress="__e" bind:__l="__l" vue-slots="{{['default']}}">加载中...</parser></scroll-view></view><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="flex-center pd-lg" bindtap="__e"><view class="iconfont icon-close c-base _i"></view></view></view></uni-popup></view></block>
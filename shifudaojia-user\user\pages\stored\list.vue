<template>
	<view class="master-income-record">
		<view class="mine-menu-list c-base" :style="{background:primaryColor}">
			<view class="space-lg"></view>
			<view class="space-lg"></view>
			<view class="flex-center f-caption mt-sm mb-sm">当前余额(元)</view>
			<view class="money-info flex-center flex-y-baseline">¥<view class="money">{{balance}}
				</view>
			</view>
			<view class="space-md"></view>
			<view class="space-lg"></view>
		</view>
		<view class="flex-center fill-base pd-lg f-paragraph b-1px-b">
			<view @tap.stop="$util.goUrl({url:`/user/pages/stored/commission`})"
				class="item-record-btn flex-center c-base mr-lg radius"
				:style="{background:primaryColor,border:`1rpx solid ${primaryColor}`}">返佣记录</view>
			<view @tap.stop="$util.goUrl({url:`/user/pages/stored/record`})"
				class="item-record-btn flex-center ml-lg mr-lg radius"
				:style="{color:primaryColor,border:`1rpx solid ${primaryColor}`}">充值记录</view>
			<view @tap.stop="$util.goUrl({url:`/user/pages/stored/consume`})"
				class="item-record-btn flex-center ml-lg radius"
				:style="{color:primaryColor,border:`1rpx solid ${primaryColor}`}">消费明细</view>
		</view>

		<view @tap="toPay(index)" class="list-item fill-base flex-between mt-md ml-lg mr-lg pd-lg radius-16"
			v-for="(item,index) in  list.data" :key="index">
			<view class="flex-1">
				<view class="f-title c-warning">¥{{item.price}}</view>
				<view class="f-caption c-caption">{{item.title}}</view>
			</view>
			<view class="item-btn flex-center f-caption c-base radius-5" :style="{background:primaryColor}">立即充值
			</view>
		</view>

		<view class="list-item fill-base mt-md ml-lg mr-lg radius-16">
			<view class="pd-lg b-1px-b">
				<view class="pl-md f-paragraph" :style="{lineHeight: 1.1,borderLeft: `5rpx solid ${primaryColor}`}">充值说明
				</view>
			</view>
			<view class="pd-lg f-caption c-caption">
				<view>1.充值的本金和赠送金额均不可提现、转移、转赠</view>
				<view>2.使用范围：本平台所有项目皆可购买，以及交通费支付</view>
			</view>
		</view>
		<view class="space-footer"></view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions,
		mapMutations
	} from "vuex"
	export default {
		components: {},
		data() {
			return {
				color: '#ffffff',
				balance: 0,
				param: {
					page: 1,
					limit: 10
				},
				list: {
					data: []
				},
				loading: true,
				lockTap: false
			}
		},
		computed: mapState({
			primaryColor: state => state.config.configInfo.primaryColor,
			subColor: state => state.config.configInfo.subColor,
			userInfo: state => state.user.userInfo,
			mineInfo: state => state.user.mineInfo,
		}),
		async onLoad() {
			this.$util.showLoading()
			let {
				balance = 0
			} = this.mineInfo
			this.balance = balance
			await this.initIndex()
		},
		onPullDownRefresh() {
			// #ifndef APP-PLUS
			uni.showNavigationBarLoading()
			// #endif
			this.initRefresh();
			uni.stopPullDownRefresh()
		},
		onReachBottom() {
			if (this.list.current_page >= this.list.last_page || this.loading) return;
			this.param.page = this.param.page + 1;
			this.loading = true;
			this.getList();
		},
		methods: {
			...mapActions(['getMineInfo']),
			...mapMutations([]),
			async initIndex(refresh = false) {
				// #ifdef H5
				if (!refresh && this.$jweixin.isWechat()) {
					await this.$jweixin.initJssdk();
					this.$jweixin.wxReady(() => {
						this.$jweixin.hideOptionMenu()
					})
				}
				// #endif
				await this.getList()
				this.$util.setNavigationBarColor({
					bg: this.primaryColor
				})
			},
			initRefresh() {
				this.param.page = 1
				this.initIndex(true)
			},
			async getList() {
				let {
					list: oldList,
					param,
				} = this
				let newList = await this.$api.mine.cardList(param);

				if (this.param.page == 1) {
					this.list = newList
				} else {
					newList.data = oldList.data.concat(newList.data)
					this.list = newList
				}
				this.loading = false
				this.$util.hideAll()
			},
			// 去支付
			async toPay(index) {
				let {
					id: card_id,
					true_price
				} = this.list.data[index]
				if (this.lockTap) return;
				this.lockTap = true;
				this.$util.showLoading()
				try {
					let {
						pay_list
					} = await this.$api.mine.payBalanceOrder({
						card_id
					})
					this.$util.hideAll()
					if(pay_list.mweb_url){
						return uni.redirectTo({
							url:'/pages/pay?url='+encodeURIComponent(pay_list.mweb_url)
						});
					}
					if (pay_list) {
						try {
							await this.$util.pay(pay_list)
							this.lockTap = false;
							await this.getMineInfo()
							this.$util.back()
						} catch (e) {
							this.lockTap = false;
							return;
						}
					}
				} catch (e) {
					setTimeout(() => {
						this.lockTap = false
						this.$util.hideAll()
					}, 2000)
				}
			},
		}
	}
</script>


<style lang="scss">
	.master-income-record {

		.mine-menu-list {

			.money-info {
				font-size: 50rpx;

				.money {
					font-size: 70rpx;
				}
			}

		}

		.item-record-btn {
			width: 178rpx;
			height: 58rpx;
			transform: rotateZ(360deg);
		}

		.list-item {
			.item-btn {
				width: 130rpx;
				height: 54rpx;
			}
		}



		.stored-item {
			.item-input {
				height: 90rpx;
				border-radius: 15rpx 0 0 15rpx;
			}

			.item-btn {
				width: 130rpx;
				height: 90rpx;
				border-radius: 0 15rpx 15rpx 0;
			}
		}

	}
</style>

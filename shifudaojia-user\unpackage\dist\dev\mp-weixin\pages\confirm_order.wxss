@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-8d2b8434 {
  padding-bottom: 200rpx;
}
.page.data-v-8d2b8434  .u-popup__content {
  display: none;
}
.page.data-v-8d2b8434  .u-number-box__plus {
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx !important;
  background-color: #fff !important;
  border: 1px solid #000;
}
.page.data-v-8d2b8434  .u-number-box__plus text {
  font-size: 24rpx !important;
  line-height: 36rpx !important;
}
.page.data-v-8d2b8434  .u-number-box__minus {
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx !important;
  background-color: #fff !important;
  border: 1px solid #000;
}
.page.data-v-8d2b8434  .u-number-box__minus text {
  font-size: 24rpx !important;
  line-height: 36rpx !important;
}
.page.data-v-8d2b8434  .u-number-box__minus--disabled {
  border: 1px solid #ADADAD;
}
.page.data-v-8d2b8434  .u-number-box__input {
  background-color: #fff !important;
}
.page .choose_yh.data-v-8d2b8434 {
  padding-top: 40rpx;
  width: 750rpx;
  height: 1106rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  opacity: 1;
  position: fixed;
  bottom: 0;
  z-index: 10088;
  transition: all 0.5s;
}
.page .choose_yh .head.data-v-8d2b8434 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  text-align: center;
  margin-bottom: 44rpx;
}
.page .choose_yh .close.data-v-8d2b8434 {
  position: absolute;
  top: 44rpx;
  right: 32rpx;
}
.page .choose_yh .close image.data-v-8d2b8434 {
  width: 37rpx;
  height: 37rpx;
}
.page .choose_yh .cou_item.data-v-8d2b8434 {
  margin: 0 auto;
  width: 690rpx;
  height: 202rpx;
  background: #DCEAFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #2E80FE;
}
.page .choose_yh .cou_item .top.data-v-8d2b8434 {
  height: 150rpx;
  display: flex;
  align-items: center;
  padding-top: 26rpx;
  padding-left: 24rpx;
  padding-right: 14rpx;
  position: relative;
  border-bottom: 2rpx dashed #2E80FE;
}
.page .choose_yh .cou_item .top .box1.data-v-8d2b8434 {
  text-align: center;
  width: 180rpx;
  font-size: 40rpx;
  font-weight: 500;
  color: #E72427;
}
.page .choose_yh .cou_item .top .box1 ._span.data-v-8d2b8434 {
  font-size: 20rpx;
}
.page .choose_yh .cou_item .top .box2.data-v-8d2b8434 {
  margin-left: 28rpx;
}
.page .choose_yh .cou_item .top .box2 text.data-v-8d2b8434 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .choose_yh .cou_item .top .box2 ._span.data-v-8d2b8434 {
  margin-top: 10rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #B2B2B2;
}
.page .choose_yh .cou_item .top .box3.data-v-8d2b8434 {
  position: absolute;
  right: 22rpx;
  top: 40rpx;
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 2rpx solid #B2B2B2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .choose_yh .cou_item .bottom.data-v-8d2b8434 {
  padding: 0 24rpx;
  height: 50rpx;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 50rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #B2B2B2;
}
.page .choose_yh .noYh.data-v-8d2b8434 {
  width: 690rpx;
  margin: 0 auto;
  margin-top: 52rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 22rpx;
}
.page .choose_yh .noYh .left.data-v-8d2b8434 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .choose_yh .noYh .right.data-v-8d2b8434 {
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 2rpx solid #B2B2B2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .choose_yh .notcan.data-v-8d2b8434 {
  margin-top: 52rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #B2B2B2;
  padding: 0 30rpx;
}
.page .choose_time.data-v-8d2b8434 {
  padding-top: 40rpx;
  width: 750rpx;
  height: 846rpx;
  background: #FFFFFF;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  opacity: 1;
  position: fixed;
  bottom: 0;
  z-index: 10088;
  transition: all 0.5s;
}
.page .choose_time .head.data-v-8d2b8434 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  text-align: center;
}
.page .choose_time .close.data-v-8d2b8434 {
  position: absolute;
  top: 44rpx;
  right: 32rpx;
}
.page .choose_time .close image.data-v-8d2b8434 {
  width: 37rpx;
  height: 37rpx;
}
.page .choose_time .date.data-v-8d2b8434 {
  margin-top: 40rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.page .choose_time .date .date_item.data-v-8d2b8434 {
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #171717;
}
.page .choose_time .date .date_item .hk.data-v-8d2b8434 {
  margin-top: 8rpx;
  width: 100%;
  height: 6rpx;
  background: #2E80FE;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  opacity: 1;
}
.page .choose_time .time_all.data-v-8d2b8434 {
  margin-top: 20rpx;
  width: 750rpx;
  height: 390rpx;
  background: #F7F7F7;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
  padding: 20rpx 32rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.page .choose_time .time_all .time_item.data-v-8d2b8434 {
  width: 334rpx;
  height: 100rpx;
  background: #FFFFFF;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  opacity: 1;
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
  line-height: 100rpx;
  border-radius: 16rpx;
}
.page .choose_time .btn.data-v-8d2b8434 {
  margin: 0 auto;
  margin-top: 28rpx;
  width: 686rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  opacity: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
  line-height: 98rpx;
}
.page .footer.data-v-8d2b8434 {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 202rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  background-color: #fff;
}
.page .footer .left.data-v-8d2b8434 {
  font-size: 40rpx;
  font-weight: 600;
  color: #E72427;
}
.page .footer .mid.data-v-8d2b8434 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 98rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: #2E80FE;
  line-height: 98rpx;
  text-align: center;
  font-weight: 700;
  border: 2rpx solid #2E80FE;
  padding: 0 15rpx;
}
.page .footer .right.data-v-8d2b8434 {
  width: 294rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  opacity: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}
.page .fg.data-v-8d2b8434 {
  height: 20rpx;
  background-color: #F3F4F5;
}
.page .address.data-v-8d2b8434 {
  height: 164rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.page .address .left .top.data-v-8d2b8434 {
  display: flex;
  align-items: center;
}
.page .address .left .top image.data-v-8d2b8434 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
}
.page .address .left .top text.data-v-8d2b8434 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 400rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .address .left .bottom.data-v-8d2b8434 {
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  padding-left: 56rpx;
  margin-top: 12rpx;
}
.page .time.data-v-8d2b8434 {
  border-top: 2rpx solid #F0F0F0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 120rpx;
  padding: 0 32rpx;
}
.page .time .left.data-v-8d2b8434 {
  display: flex;
  align-items: center;
}
.page .time .left image.data-v-8d2b8434 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
}
.page .time .left text.data-v-8d2b8434 {
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .main.data-v-8d2b8434 {
  padding: 40rpx 32rpx;
  position: relative;
  padding-bottom: 70rpx;
}
.page .main .expand.data-v-8d2b8434 {
  width: 690rpx;
  margin: 0 auto;
  font-size: 28rpx;
  font-weight: 400;
  color: #ADADAD;
  text-align: center;
  position: absolute;
  bottom: 0;
}
.page .main .expand .icon_box.data-v-8d2b8434 {
  display: flex;
  justify-content: center;
}
.page .main .main_item.data-v-8d2b8434 {
  display: flex;
}
.page .main .main_item image.data-v-8d2b8434 {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
}
.page .main .main_item .right.data-v-8d2b8434 {
  flex: 1;
}
.page .main .main_item .right .title.data-v-8d2b8434 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .right .price.data-v-8d2b8434 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 80rpx;
}
.page .main .main_item .right .price text.data-v-8d2b8434 {
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .notes.data-v-8d2b8434 {
  padding: 40rpx 32rpx;
}
.page .notes .title.data-v-8d2b8434 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.page .notes textarea.data-v-8d2b8434 {
  margin-top: 40rpx;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  width: 686rpx;
  height: 242rpx;
  background: #F7F7F7;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
}
.page .preferential.data-v-8d2b8434 {
  display: flex;
  justify-content: space-between;
  padding: 40rpx 32rpx;
  align-items: center;
}
.page .preferential .left.data-v-8d2b8434 {
  font-size: 24rpx;
  font-weight: 400;
  color: #333333;
}
.page .preferential .right.data-v-8d2b8434 {
  display: flex;
  align-items: center;
}
.page .preferential .right text.data-v-8d2b8434 {
  font-size: 24rpx;
  font-weight: 400;
  color: #E72427;
}

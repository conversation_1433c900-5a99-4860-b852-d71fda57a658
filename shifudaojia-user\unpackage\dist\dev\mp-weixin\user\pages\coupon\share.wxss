@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-coupon-share .coupon-img {
  width: 750rpx;
  height: 560rpx;
}
.user-coupon-share .coupon-content {
  width: 750rpx;
  min-height: 1431rpx;
  background: linear-gradient(0deg, #A40035 0%, #FFAE89 100%);
}
.user-coupon-share .coupon-content .count-down {
  height: 56rpx;
}
.user-coupon-share .coupon-content .menu-img {
  width: 426rpx;
  height: 100rpx;
}
.user-coupon-share .coupon-content .menu-img.abs {
  top: -56rpx;
  left: 50%;
  margin-left: -213rpx;
}
.user-coupon-share .coupon-content .menu-title {
  width: 426rpx;
  height: 90rpx;
  font-size: 36rpx;
  top: 0;
}
.user-coupon-share .coupon-content .line-img {
  width: 630rpx;
  height: 1rpx;
}
.user-coupon-share .coupon-content .user-item {
  width: 360rpx;
}
.user-coupon-share .coupon-content .time-item {
  width: 220rpx;
}
.user-coupon-share .coupon-content .user-list-info {
  height: 576rpx;
}
.user-coupon-share .coupon-content .user-list-info .user-list .avatar {
  width: 56rpx;
  height: 56rpx;
  background: #FFAE89;
}
.user-coupon-share .coupon-content .share-img {
  width: 541rpx;
  height: 140rpx;
  margin: 0 auto;
}
.user-coupon-share .coupon-content .share-title {
  width: 541rpx;
  height: 125rpx;
  font-size: 36rpx;
  top: 0;
}
.user-coupon-share .coupon-content .atv-rule {
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
}
.user-coupon-share .popup-share .list-item {
  width: 50%;
}
.user-coupon-share .popup-share .list-item .item-image {
  width: 66rpx;
  height: 66rpx;
}

<view class="user-address-list"><block wx:for="{{list.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toUpdateItem',[index]]]]]}}" class="item-child mt-md pl-lg pr-lg fill-base radius-16" catchtap="__e"><view class="flex-warp pt-lg pb-lg b-1px-b"><view class="address-icon flex-center c-base radius" style="{{'background:'+('linear-gradient(to right, '+subColor+', '+primaryColor+')')+';'}}"><view class="iconfont iconjuli _i"></view></view><view class="address-info flex-1 ml-md"><view class="flex-y-baseline username c-title text-bold">{{item.user_name+''}}<view class="ml-md f-desc c-caption">{{item.mobile}}</view></view><view class="f-desc c-title ellipsis">{{item.address+' '+item.address_info}}</view></view></view><view class="oper-info f-paragraph c-caption flex-between"><view class="flex-y-center" style="{{'color:'+(options.check&&item.id==check_id||!options.check&&item.status==1?primaryColor:'')+';'}}"><view class="{{['iconfont','icon-xuanze','mr-sm','_i',[(options.check&&item.id==check_id||!options.check&&item.status==1)?'icon-xuanze-fill':'']]}}" style="{{'color:'+(options.check&&item.id==check_id||!options.check&&item.status==1?primaryColor:'')+';'}}"></view><block wx:if="{{options.check}}"><block>{{item.id==check_id?'当前选择地址':'点击选择'}}</block></block><block wx:else><block>{{item.status==1?'默认地址':'设为默认'}}</block></block></view><view class="flex-center"><view data-event-opts="{{[['tap',[['toDel',[index]]]]]}}" class="pl-lg pr-lg" catchtap="__e">删除</view><view data-event-opts="{{[['tap',[['goDetail',[index]]]]]}}" class="pl-lg" catchtap="__e">编辑</view></view></view></view></block><block wx:if="{{loading}}"><load-more vue-id="441efb48-1" noMore="{{$root.g0}}" loading="{{loading}}" bind:__l="__l"></load-more></block><block wx:if="{{$root.g1}}"><abnor vue-id="441efb48-2" tip="{{[{text:'你还没有添加地址哦~',color:0}]}}" bind:__l="__l"></abnor></block><view class="space-max-footer"></view><fix-bottom-button vue-id="441efb48-3" text="{{[{text:'添加新地址',type:'confirm'}]}}" bgColor="#fff" data-event-opts="{{[['^confirm',[['e0']]]]}}" bind:confirm="__e" bind:__l="__l"></fix-bottom-button></view>
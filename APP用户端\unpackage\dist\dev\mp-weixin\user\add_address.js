require('./common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["user/add_address"],{

/***/ 305:
/*!**************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/main.js?{"page":"user%2Fadd_address"} ***!
  \**************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
__webpack_require__(/*! @dcloudio/uni-stat/dist/uni-stat.es.js */ 27);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _add_address = _interopRequireDefault(__webpack_require__(/*! ./user/add_address.vue */ 306));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_add_address.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 306:
/*!*********************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue ***!
  \*********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./add_address.vue?vue&type=template&id=99ba6ec0&scoped=true& */ 307);
/* harmony import */ var _add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./add_address.vue?vue&type=script&lang=js& */ 309);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _add_address_vue_vue_type_style_index_0_id_99ba6ec0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss& */ 311);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 66);

var renderjs





/* normalize component */

var component = Object(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "99ba6ec0",
  null,
  false,
  _add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "user/add_address.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 307:
/*!****************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?vue&type=template&id=99ba6ec0&scoped=true& ***!
  \****************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add_address.vue?vue&type=template&id=99ba6ec0&scoped=true& */ 308);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_template_id_99ba6ec0_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 308:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?vue&type=template&id=99ba6ec0&scoped=true& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uPicker: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-picker/u-picker */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-picker/u-picker")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-picker/u-picker.vue */ 919))
    },
    uSwitch: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-switch/u-switch */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-switch/u-switch")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-switch/u-switch.vue */ 927))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.showCity = false
    }
  }
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 309:
/*!**********************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add_address.vue?vue&type=script&lang=js& */ 310);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 310:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 36));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 38));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      flag: false,
      loading: false,
      showCity: false,
      menpai: '',
      form: {
        userName: '',
        mobile: '',
        address: '点击选择服务地址',
        addressInfo: '',
        houseNumber: '',
        city: '',
        cityIds: '',
        lng: '',
        lat: '',
        sex: 1,
        status: false,
        provinceId: 0,
        cityId: 0,
        areaId: 0
      },
      columnsCity: [[],
      // Province
      [],
      // City
      [] // Area
      ]
    };
  },
  onLoad: function onLoad() {
    // this.getCity(0)
    this.getNowPosition();
    this.checkAppVersion();
  },
  methods: {
    // 性别选择方法
    selectGender: function selectGender(gender) {
      this.form.sex = gender;
      console.log('Selected gender:', gender, 'Current form.sex:', this.form.sex);
      // 强制触发视图更新
      this.$forceUpdate();
    },
    // 解析城市信息的通用方法
    parseCityInfo: function parseCityInfo(address) {
      if (!address || typeof address !== 'string') {
        return {
          cityIds: '',
          city: ''
        };
      }

      // 处理各种地址格式的正则表达式
      var patterns = [
      // 标准格式：省+市+区/县
      /^(.+?省)(.+?市)(.+?[县区]).*$/,
      // 自治区格式：自治区+市+区/县/旗
      /^(.+?自治区)(.+?市)(.+?[县区旗]).*$/,
      // 自治区+盟+市格式：自治区+盟+市
      /^(.+?自治区)(.+?盟)(.+?市).*$/,
      // 直辖市格式：市+区/县
      /^(北京|上海|天津|重庆)(市)?(.+?[县区]).*$/,
      // 特别行政区格式
      /^(香港|澳门)(.+?区)?(.*)$/,
      // 简化格式：只有县/区+详细地址（苹果小程序常见格式）
      /^(.+?[县区市])(.+)$/,
      // 更简化格式：只有县/区名称
      /^(.+?[县区市])$/];
      for (var _i = 0, _patterns = patterns; _i < _patterns.length; _i++) {
        var pattern = _patterns[_i];
        var match = address.match(pattern);
        if (match) {
          var province = void 0,
            city = void 0,
            area = void 0;
          if (pattern.source.includes('北京|上海|天津|重庆')) {
            // 直辖市处理
            province = match[1];
            city = match[1] + '市';
            area = match[3] || '';
          } else if (pattern.source.includes('香港|澳门')) {
            // 特别行政区处理
            province = match[1];
            city = match[1];
            area = match[2] || match[3] || '';
          } else if (pattern.source.includes('盟')) {
            // 自治区+盟+市格式处理
            province = match[1];
            city = match[2]; // 盟作为市级
            area = match[3]; // 市作为区级
          } else if (pattern.source.includes('\\+\\?\\[县区市\\]\\)\\(\\.\\\+\\)\\$')) {
            // 简化格式：县/区+详细地址（如：临泉县兴园路附近）
            province = ''; // 省份信息缺失
            city = ''; // 市级信息缺失
            area = match[1] || ''; // 县/区信息
          } else if (pattern.source.includes('\\+\\?\\[县区市\\]\\)\\$')) {
            // 更简化格式：只有县/区名称
            province = '';
            city = '';
            area = match[1] || '';
          } else {
            // 标准省市区处理
            province = match[1] || '';
            city = match[2] || '';
            area = match[3] || '';
          }

          // 清理空白字符，确保变量不为 undefined
          province = (province || '').trim();
          city = (city || '').trim();
          area = (area || '').trim();
          return {
            cityIds: "".concat(province, ",").concat(city, ",").concat(area),
            city: "".concat(province, "-").concat(city, "-").concat(area)
          };
        }
      }

      // 如果都不匹配，返回空值
      console.warn('无法解析地址格式:', address);
      return {
        cityIds: '',
        city: ''
      };
    },
    // 处理苹果小程序简化地址的方法
    handleSimplifiedAddress: function handleSimplifiedAddress(res, that) {
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var cityInfo, isIncompleteAddress, geoRes, completeCityInfo;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                // 首先尝试解析地址信息
                cityInfo = {
                  cityIds: '',
                  city: ''
                };
                if (res.address) {
                  cityInfo = that.parseCityInfo(res.address);
                }

                // 检查是否为简化地址格式（缺少省市信息）
                isIncompleteAddress = !cityInfo.cityIds || cityInfo.cityIds.split(',').some(function (part) {
                  return !part || part.trim() === '';
                });
                if (!(isIncompleteAddress && res.longitude && res.latitude)) {
                  _context.next = 12;
                  break;
                }
                console.log('检测到简化地址格式，使用逆地理编码获取完整信息');
                // 使用高德地图API获取完整地址信息
                _context.next = 8;
                return uni.request({
                  url: "https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=".concat(res.longitude, ",").concat(res.latitude),
                  method: 'GET'
                });
              case 8:
                geoRes = _context.sent;
                if (geoRes.data && geoRes.data.regeocode && geoRes.data.regeocode.formatted_address) {
                  completeCityInfo = that.parseCityInfo(geoRes.data.regeocode.formatted_address);
                  that.form.cityIds = completeCityInfo.cityIds;
                  that.form.city = completeCityInfo.city;
                  that.form.addressInfo = geoRes.data.regeocode.formatted_address;
                  console.log('逆地理编码获取的完整cityIds:', that.form.cityIds);
                  console.log('逆地理编码获取的完整city:', that.form.city);
                } else {
                  // 如果逆地理编码也失败，使用原始解析结果
                  that.form.cityIds = cityInfo.cityIds;
                  that.form.city = cityInfo.city;
                  that.form.addressInfo = res.address || '';
                }
                _context.next = 15;
                break;
              case 12:
                // 地址信息完整，直接使用解析结果
                that.form.cityIds = cityInfo.cityIds;
                that.form.city = cityInfo.city;
                that.form.addressInfo = res.address || '';
              case 15:
                // 设置其他信息
                that.form.address = res.name || '未知位置';
                that.form.lng = res.longitude || '';
                that.form.lat = res.latitude || '';
                console.log('最终处理后的cityIds:', that.form.cityIds);
                console.log('最终处理后的city:', that.form.city);
                return _context.abrupt("return", true);
              case 23:
                _context.prev = 23;
                _context.t0 = _context["catch"](0);
                console.error('处理简化地址时出错:', _context.t0);
                return _context.abrupt("return", false);
              case 27:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 23]]);
      }))();
    },
    getNowPosition: function getNowPosition() {
      var _this = this;
      return new Promise(function (resolve) {
        uni.getLocation({
          type: "gcj02",
          isHighAccuracy: true,
          accuracy: "best",
          success: function success(res) {
            uni.setStorageSync("lat", res.latitude);
            uni.setStorageSync("lng", res.longitude);
            uni.request({
              url: "https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=".concat(res.longitude, ",").concat(res.latitude),
              success: function success(res1) {
                console.log(res1);
                _this.form.address = res1.data.regeocode.formatted_address;
                // 使用新的城市信息解析方法
                var cityInfo = _this.parseCityInfo(res1.data.regeocode.formatted_address);
                _this.form.cityIds = cityInfo.cityIds;
                _this.form.city = cityInfo.city;
                // Store coordinates
                _this.form.lng = res.longitude;
                _this.form.lat = res.latitude;
                resolve();
              },
              fail: function fail(err) {
                console.error("逆地理编码失败:", err);
                resolve();
              }
            });
          },
          fail: function fail(err) {
            console.error("获取定位失败:", err);
            resolve();
          }
        });
      });
    },
    goMap: function goMap() {
      var that = this;
      uni.authorize({
        scope: 'scope.userLocation',
        success: function success(res) {
          console.log(res);
          // 添加延时和错误处理
          setTimeout(function () {
            uni.chooseLocation({
              success: function () {
                var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2(res) {
                  var cityInfo, isIncompleteAddress, geoRes, completeCityInfo;
                  return _regenerator.default.wrap(function _callee2$(_context2) {
                    while (1) {
                      switch (_context2.prev = _context2.next) {
                        case 0:
                          console.log('选择位置成功:', res);
                          _context2.prev = 1;
                          // 直接处理地址信息，不使用单独的函数
                          cityInfo = {
                            cityIds: '',
                            city: ''
                          };
                          if (res.address) {
                            cityInfo = that.parseCityInfo(res.address);
                          }

                          // 检查是否为简化地址格式（缺少省市信息）
                          isIncompleteAddress = !cityInfo.cityIds || cityInfo.cityIds.split(',').some(function (part) {
                            return !part || part.trim() === '';
                          });
                          if (!(isIncompleteAddress && res.longitude && res.latitude)) {
                            _context2.next = 22;
                            break;
                          }
                          console.log('检测到简化地址格式，使用逆地理编码获取完整信息');
                          // 使用高德地图API获取完整地址信息
                          _context2.prev = 7;
                          _context2.next = 10;
                          return uni.request({
                            url: "https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=".concat(res.longitude, ",").concat(res.latitude),
                            method: 'GET'
                          });
                        case 10:
                          geoRes = _context2.sent;
                          if (geoRes.data && geoRes.data.regeocode && geoRes.data.regeocode.formatted_address) {
                            completeCityInfo = that.parseCityInfo(geoRes.data.regeocode.formatted_address);
                            that.form.cityIds = completeCityInfo.cityIds;
                            that.form.city = completeCityInfo.city;
                            that.form.addressInfo = geoRes.data.regeocode.formatted_address;
                            console.log('逆地理编码获取的完整cityIds:', that.form.cityIds);
                            console.log('逆地理编码获取的完整city:', that.form.city);
                          } else {
                            // 如果逆地理编码也失败，使用原始解析结果
                            that.form.cityIds = cityInfo.cityIds;
                            that.form.city = cityInfo.city;
                            that.form.addressInfo = res.address || '';
                          }
                          _context2.next = 20;
                          break;
                        case 14:
                          _context2.prev = 14;
                          _context2.t0 = _context2["catch"](7);
                          console.error('逆地理编码失败:', _context2.t0);
                          // 使用原始解析结果
                          that.form.cityIds = cityInfo.cityIds;
                          that.form.city = cityInfo.city;
                          that.form.addressInfo = res.address || '';
                        case 20:
                          _context2.next = 25;
                          break;
                        case 22:
                          // 地址信息完整，直接使用解析结果
                          that.form.cityIds = cityInfo.cityIds;
                          that.form.city = cityInfo.city;
                          that.form.addressInfo = res.address || '';
                        case 25:
                          // 设置其他信息
                          that.form.address = res.name || '未知位置';
                          that.form.lng = res.longitude || '';
                          that.form.lat = res.latitude || '';
                          console.log('最终处理后的cityIds:', that.form.cityIds);
                          console.log('最终处理后的city:', that.form.city);
                          uni.showToast({
                            title: '位置选择成功',
                            icon: 'success',
                            duration: 1500
                          });
                          _context2.next = 43;
                          break;
                        case 33:
                          _context2.prev = 33;
                          _context2.t1 = _context2["catch"](1);
                          console.error('处理位置信息时出错:', _context2.t1);
                          // 回退到基本处理
                          that.form.cityIds = res.address ? that.parseCityInfo(res.address).cityIds : '';
                          that.form.city = res.address ? that.parseCityInfo(res.address).city : '';
                          that.form.address = res.name || '未知位置';
                          that.form.addressInfo = res.address || '';
                          that.form.lng = res.longitude || '';
                          that.form.lat = res.latitude || '';
                          uni.showToast({
                            title: '位置信息处理失败，但已保存基本信息',
                            icon: 'none',
                            duration: 2000
                          });
                        case 43:
                        case "end":
                          return _context2.stop();
                      }
                    }
                  }, _callee2, null, [[1, 33], [7, 14]]);
                }));
                function success(_x) {
                  return _success.apply(this, arguments);
                }
                return success;
              }(),
              fail: function fail(err) {
                console.error('选择位置失败:', err);
                uni.showToast({
                  title: '选择位置失败，请重试',
                  icon: 'none',
                  duration: 2000
                });
              }
            });
          }, 300); // 延时300ms避免框架内部状态问题
        },
        fail: function fail(err) {
          console.error('位置授权失败:', err);
          uni.showToast({
            title: '请授权位置信息',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
    // 备用的位置获取方法
    getLocationFallback: function getLocationFallback() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var res, geoRes, cityInfo;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _context3.next = 3;
                return uni.getLocation({
                  type: 'gcj02',
                  isHighAccuracy: true,
                  accuracy: 'best'
                });
              case 3:
                res = _context3.sent;
                _context3.next = 6;
                return uni.request({
                  url: "https://restapi.amap.com/v3/geocode/regeo?key=4272f5716dfd17882409f306c0299666&location=".concat(res.longitude, ",").concat(res.latitude),
                  method: 'GET'
                });
              case 6:
                geoRes = _context3.sent;
                if (geoRes.data && geoRes.data.regeocode) {
                  _this2.form.address = geoRes.data.regeocode.formatted_address || '当前位置';
                  _this2.form.addressInfo = geoRes.data.regeocode.formatted_address || '';
                  _this2.form.lng = res.longitude;
                  _this2.form.lat = res.latitude;

                  // 处理城市信息
                  if (geoRes.data.regeocode.formatted_address) {
                    cityInfo = _this2.parseCityInfo(geoRes.data.regeocode.formatted_address);
                    _this2.form.cityIds = cityInfo.cityIds;
                    _this2.form.city = cityInfo.city;
                  }
                  uni.showToast({
                    title: '已获取当前位置',
                    icon: 'success',
                    duration: 1500
                  });
                }
                _context3.next = 14;
                break;
              case 10:
                _context3.prev = 10;
                _context3.t0 = _context3["catch"](0);
                console.error('备用位置获取失败:', _context3.t0);
                uni.showToast({
                  title: '位置获取失败，请手动输入',
                  icon: 'none',
                  duration: 2000
                });
              case 14:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 10]]);
      }))();
    },
    confirmCity: function confirmCity(Array) {
      var _this3 = this;
      // Map selected values to titles and IDs
      var selectedValues = Array.value;
      var titles = selectedValues.map(function (item, index) {
        var _this3$columnsCity$in;
        return (item === null || item === void 0 ? void 0 : item.title) || ((_this3$columnsCity$in = _this3.columnsCity[index][0]) === null || _this3$columnsCity$in === void 0 ? void 0 : _this3$columnsCity$in.title) || '';
      });
      var ids = selectedValues.map(function (item, index) {
        var _this3$columnsCity$in2;
        return (item === null || item === void 0 ? void 0 : item.id) || ((_this3$columnsCity$in2 = _this3.columnsCity[index][0]) === null || _this3$columnsCity$in2 === void 0 ? void 0 : _this3$columnsCity$in2.id) || 0;
      }).filter(function (id) {
        return id !== null && id !== undefined;
      });
      this.form.city = titles.join('-');
      // Set cityIds as nested array [[provinceId, cityId, areaId]]
      this.form.cityIds = ids.length >= 3 ? [[ids[0], ids[1], ids[2]]] : [[0, 0, 0]];
      // Set individual IDs
      this.form.provinceId = ids[0] || 0;
      this.form.cityId = ids[1] || 0;
      this.form.areaId = ids[2] || 0;
      this.showCity = false;
    },
    getCity: function getCity(e) {
      var _this4 = this;
      this.$api.service.getCity(e).then(function (res) {
        var _res$;
        _this4.columnsCity[0] = res;
        if ((_res$ = res[0]) !== null && _res$ !== void 0 && _res$.id) {
          _this4.$api.service.getCity(res[0].id).then(function (res1) {
            var _res1$;
            _this4.columnsCity[1] = res1;
            if ((_res1$ = res1[0]) !== null && _res1$ !== void 0 && _res1$.id) {
              _this4.$api.service.getCity(res1[0].id).then(function (res2) {
                _this4.columnsCity[2] = res2;
                _this4.flag = true;
              });
            }
          });
        }
      }).catch(function (err) {
        console.error('Failed to fetch city data:', err);
      });
    },
    changeHandler: function changeHandler(e) {
      var _this5 = this;
      var columnIndex = e.columnIndex,
        index = e.index,
        _e$picker = e.picker,
        picker = _e$picker === void 0 ? this.$refs.uPicker : _e$picker;
      if (columnIndex === 0) {
        var _this$columnsCity$0$i;
        if ((_this$columnsCity$0$i = this.columnsCity[0][index]) !== null && _this$columnsCity$0$i !== void 0 && _this$columnsCity$0$i.id) {
          this.$api.service.getCity(this.columnsCity[0][index].id).then(function (res) {
            var _res$2;
            picker.setColumnValues(1, res);
            _this5.columnsCity[1] = res;
            if ((_res$2 = res[0]) !== null && _res$2 !== void 0 && _res$2.id) {
              _this5.$api.service.getCity(res[0].id).then(function (res1) {
                picker.setColumnValues(2, res1);
                _this5.columnsCity[2] = res1;
                console.log(res1);
              });
            }
          });
        }
      } else if (columnIndex === 1) {
        var _this$columnsCity$1$i;
        if ((_this$columnsCity$1$i = this.columnsCity[1][index]) !== null && _this$columnsCity$1$i !== void 0 && _this$columnsCity$1$i.id) {
          this.$api.service.getCity(this.columnsCity[1][index].id).then(function (res) {
            picker.setColumnValues(2, res);
            _this5.columnsCity[2] = res;
            console.log(res);
          });
        }
      }
    },
    SaveAddress: function SaveAddress() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var phoneReg, _i2, _arr, key, res, userId, subForm, _res;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                if (!(_this6.form.address === '点击选择服务地址')) {
                  _context4.next = 3;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '请填写完整提交',
                  findduration: 1500
                });
                return _context4.abrupt("return");
              case 3:
                phoneReg = /^1[3456789]\d{9}$/;
                if (phoneReg.test(_this6.form.mobile)) {
                  _context4.next = 7;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '请填写正确的手机号',
                  duration: 1500
                });
                return _context4.abrupt("return");
              case 7:
                console.log(_this6.form);
                _i2 = 0, _arr = ['userName', 'mobile', 'address', 'houseNumber'];
              case 9:
                if (!(_i2 < _arr.length)) {
                  _context4.next = 17;
                  break;
                }
                key = _arr[_i2];
                if (!(_this6.form[key] === '')) {
                  _context4.next = 14;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '请填写完整提交',
                  duration: 1500
                });
                return _context4.abrupt("return");
              case 14:
                _i2++;
                _context4.next = 9;
                break;
              case 17:
                if (!(!_this6.form.cityIds || _this6.form.cityIds === '')) {
                  _context4.next = 20;
                  break;
                }
                uni.showToast({
                  icon: 'none',
                  title: '请选择所在区域',
                  duration: 1500
                });
                return _context4.abrupt("return");
              case 20:
                if (!_this6.form.cityIds) {
                  _context4.next = 34;
                  break;
                }
                _context4.prev = 21;
                _context4.next = 24;
                return _this6.$api.service.getZhuanhuan({
                  mergeName: _this6.form.cityIds
                });
              case 24:
                res = _context4.sent;
                console.log(res);
                if (res.data) {
                  // Construct the comma-separated string from the individual IDs
                  _this6.form.cityIds = "".concat(res.data.provinceId, ",").concat(res.data.cityId, ",").concat(res.data.areaId);
                  console.log(_this6.form.cityIds);
                } else {
                  _this6.form.cityIds = ''; // Handle cases where res.data might be null or undefined
                }
                _context4.next = 34;
                break;
              case 29:
                _context4.prev = 29;
                _context4.t0 = _context4["catch"](21);
                console.error("Error converting cityIds:", _context4.t0);
                uni.showToast({
                  icon: 'none',
                  title: '城市信息转换失败',
                  duration: 1500
                });
                return _context4.abrupt("return");
              case 34:
                // Prepare form data for API
                userId = uni.getStorageSync('userInfo');
                console.log(userId);
                console.log(_this6.form);
                // console.log(JSON.parse(userId))
                subForm = {
                  address: _this6.form.address,
                  addressInfo: _this6.form.addressInfo,
                  // areaId: this.form.areaId,
                  city: _this6.form.city,
                  // cityId: this.form.cityId,
                  cityIds: _this6.form.cityIds,
                  // [[provinceId, cityId, areaId]]
                  // createTime: 0,
                  houseNumber: _this6.form.houseNumber,
                  // id: 0,
                  lat: _this6.form.lat,
                  lng: _this6.form.lng,
                  mobile: _this6.form.mobile,
                  // provinceId: this.form.provinceId,
                  sex: _this6.form.sex,
                  status: _this6.form.status ? 1 : 0,
                  // top: 0,
                  // uniacid: this.form.uniacid,

                  // userId:userId.userId,
                  userName: _this6.form.userName
                };
                _context4.prev = 38;
                _context4.next = 41;
                return _this6.$api.service.postAddAddress(subForm);
              case 41:
                _res = _context4.sent;
                console.log('Add address response:', _res);
                if (_res.code === '200') {
                  uni.showToast({
                    icon: 'success',
                    title: '提交成功',
                    duration: 1000
                  });
                  setTimeout(function () {
                    uni.navigateBack({
                      delta: 1
                    });
                  }, 1000);
                } else {
                  console.error('Add address failed with code:', _res.code, 'message:', _res.msg);
                  uni.showToast({
                    icon: 'none',
                    title: _res.msg || '提交失败，请重新尝试',
                    duration: 1500
                  });
                }
                _context4.next = 50;
                break;
              case 46:
                _context4.prev = 46;
                _context4.t1 = _context4["catch"](38);
                console.error('Add address error:', _context4.t1);
                uni.showToast({
                  icon: 'none',
                  title: _context4.t1.msg || _context4.t1.message || '网络错误，请重试',
                  duration: 1500
                });
              case 50:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[21, 29], [38, 46]]);
      }))();
    },
    // 检查APP版本和环境
    checkAppVersion: function checkAppVersion() {}
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 311:
/*!*******************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss& ***!
  \*******************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_style_index_0_id_99ba6ec0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss& */ 312);
/* harmony import */ var _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_style_index_0_id_99ba6ec0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_style_index_0_id_99ba6ec0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_style_index_0_id_99ba6ec0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_style_index_0_id_99ba6ec0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_E_BaiduNetdiskDownload_HBuilderX_4_45_2025010502_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_add_address_vue_vue_type_style_index_0_id_99ba6ec0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 312:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/add_address.vue?vue&type=style&index=0&id=99ba6ec0&scoped=true&lang=scss& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[305,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/user/add_address.js.map
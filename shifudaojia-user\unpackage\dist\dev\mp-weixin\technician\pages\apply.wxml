<block wx:if="{{isLoad}}"><view class="apply-pages"><block wx:if="{{$root.g0}}"><view class="page-height"><abnor vue-id="26a06d00-1" percent="150%" title="{{title[coach_status]}}" tip="{{tipArr[coach_status]}}" button="{{buttonArr[coach_status]}}" image="{{image[coach_status]}}" data-event-opts="{{[['^confirm',[['confirm']]],['^cancel',[['e0']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:__l="__l"></abnor></view></block><block wx:else><block><view class="apply-form"><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg b-1px-b"><view class="item-text">姓名</view><input class="item-input flex-1" type="text" maxlength="20" placeholder="{{rule[0].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','coach_name','$event',[]],['form']]]]]}}" value="{{form.coach_name}}" bindinput="__e"/></view><view class="flex-between pl-lg pr-lg b-1px-b"><view class="item-text">性别</view><view class="item-input flex-1 flex-y-center"><block wx:for="{{['男','女']}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({index})}}" class="{{['flex-y-center',[(index==0)?'mr-lg':'']]}}" style="{{'color:'+(form.sex==index?primaryColor:'')+';'}}" catchtap="__e"><view class="{{['iconfont','icon-xuanze','mr-sm','_i',[(form.sex==index)?'icon-xuanze-fill':'']]}}"></view>{{item+''}}</view></block></view></view><view class="flex-between pl-lg pr-lg b-1px-b"><view class="item-text">手机号</view><input class="item-input flex-1" type="text" placeholder="{{rule[1].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e"/></view><view class="flex-between pl-lg pr-lg b-1px-b"><view class="item-text">从业年份</view><input class="item-input flex-1" type="number" placeholder="{{rule[2].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','work_time','$event',[]],['form']]]]]}}" value="{{form.work_time}}" bindinput="__e"/></view><view class="flex-between pl-lg pr-lg b-1px-b"><view class="item-text">意向工作城市</view><view class="item-input text"><picker value="{{cityIndex}}" range="{{cityList}}" range-key="title" data-event-opts="{{[['change',[['pickerChange',['$event']]]]]}}" bindchange="__e"><view class="flex-y-center">{{''+(cityIndex!=-1?cityList[cityIndex].title:'请选择')+''}}<view class="iconfont icon-right ml-sm _i" style="font-size:28rpx;"></view></view></picker></view></view><view class="flex-between pl-lg pr-lg"><view class="item-text">所在地址</view><view class="item-input text flex-1"><view data-event-opts="{{[['tap',[['toChooseLocation',['$event']]]]]}}" class="flex-y-center text-right" catchtap="__e"><view class="flex-1 text-right">{{form.address||'点击右边图标设置'}}</view><view class="iconfont iconjuli ml-sm _i" style="{{'color:'+(primaryColor)+';'}}"></view></view></view></view></view><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg"><view class="item-text">技师简介</view><input class="item-input flex-1" disabled="{{true}}" type="text"/></view><textarea class="item-textarea pd-lg" maxlength="300" placeholder="输入技师简介" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['form']]]]]}}" value="{{form.text}}" bindinput="__e"></textarea><view class="text-right pb-lg pr-lg">{{''+($root.g1>300?300:$root.g2)+'/300'}}</view></view><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg"><view class="item-text">身份证号</view><input class="item-input flex-1" type="text" placeholder="{{rule[6].errorMsg}}" data-event-opts="{{[['input',[['__set_model',['$0','id_code','$event',[]],['form']]]]]}}" value="{{form.id_code}}" bindinput="__e"/></view></view><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg"><view class="item-text">身份证照片</view><input class="item-input flex-1" disabled="{{true}}" type="text"/></view><view class="flex-between pl-lg pr-lg pb-md"><upload vue-id="26a06d00-2" imagelist="{{form.id_card}}" imgtype="id_card" imgclass="md" text="身份证人像面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" bind:__l="__l"></upload><upload vue-id="26a06d00-3" imagelist="{{form.id_card_fan}}" imgtype="id_card_fan" imgclass="md" text="身份证国徽面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" bind:__l="__l"></upload></view><view class="flex-between pl-lg pr-lg pb-md"><upload vue-id="26a06d00-4" imagelist="{{form.id_card_people}}" imgtype="id_card_people" imgclass="md" text="手持身份证照片" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" bind:__l="__l"></upload></view></view><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg"><view class="item-text">资格证书</view><input class="item-input flex-1" disabled="{{true}}" type="text"/></view><view class="flex-between pl-lg pr-lg pb-md"><upload vue-id="26a06d00-5" imagelist="{{form.license}}" imgtype="license" text="上传图片" imgsize="{{15}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" bind:__l="__l"></upload></view></view><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg"><view class="item-text">工作形象照</view><input class="item-input flex-1" disabled="{{true}}" type="text"/></view><view class="flex-between pl-lg pr-lg pb-md"><upload vue-id="26a06d00-6" imagelist="{{form.work_img}}" imgtype="work_img" text="上传图片" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" bind:__l="__l"></upload></view></view><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg"><view class="item-text">个人生活照</view><input class="item-input flex-1" disabled="{{true}}" type="text"/></view><view class="flex-between pl-lg pr-lg pb-md"><upload vue-id="26a06d00-7" imagelist="{{form.self_img}}" filetype="picture" imgtype="self_img" text="上传图片" imgsize="{{9}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" bind:__l="__l"></upload></view></view><view class="fill-base mt-md radius-16"><view class="flex-between pl-lg pr-lg"><view class="item-text">个人视频介绍</view><input class="item-input flex-1" disabled="{{true}}" type="text"/></view><view class="flex-between pl-lg pr-lg pb-md"><upload vue-id="26a06d00-8" imagelist="{{form.video}}" filetype="video" imgtype="video" text="上传视频" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" bind:__l="__l"></upload></view></view><view class="flex-center f-caption c-caption pd-lg">{{''+(options.is_edit==1?'编辑资料将进入重新审核，审核通过之前将显示原资料':'平台不会通过任何渠道泄露您的个人信息，请放心输入')+''}}</view></view><view class="space-max-footer"></view><auth vue-id="26a06d00-9" needAuth="{{userInfo&&!userInfo.nickName}}" must="{{true}}" type="userInfo" data-event-opts="{{[['^go',[['submit']]]]}}" bind:go="__e" bind:__l="__l" vue-slots="{{['default']}}"><fix-bottom-button vue-id="{{('26a06d00-10')+','+('26a06d00-9')}}" text="{{[{text:'确定申请',type:'confirm'}]}}" bgColor="#fff" bind:__l="__l"></fix-bottom-button></auth></block></block></view></block>
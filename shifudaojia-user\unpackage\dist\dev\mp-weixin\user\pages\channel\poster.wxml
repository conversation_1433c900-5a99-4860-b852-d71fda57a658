<view style="padding:20rpx 0;"><view class="hideCanvasView"><l-painter class="hideCanvas vue-ref" vue-id="005d0a9c-1" data-ref="painter" bind:__l="__l"></l-painter></view><block wx:if="{{src}}"><block><image class="code-img" src="{{src}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="btns"><button data-event-opts="{{[['tap',[['saveImage',['$event']]]]]}}" class="save-btn flex-center radius" style="{{'background:'+(primaryColor)+';'}}" bindtap="__e">保存图片至相册</button></view></block></block></view>
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.order-pages .list-item .item-img {
  width: 140rpx;
  height: 140rpx;
  background: #f4f6f8;
}
.order-pages .list-item .ellipsis {
  max-width: 466rpx;
}
.order-pages .list-item .item-tag {
  width: 100rpx;
  height: 36rpx;
  margin-top: -18rpx;
}
.order-pages .list-item .iconyduixingxingshixin {
  font-size: 28rpx;
}
.order-pages .list-item .item-btn {
  width: 129rpx;
  height: 54rpx;
}
.order-pages .pay-info {
  height: 110rpx;
  bottom: 0;
  height: calc(110rpx + env(safe-area-inset-bottom) / 2);
  padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}
.order-pages .pay-info .pay-btn {
  width: 182rpx;
  height: 74rpx;
}
.order-pages .popup-rule {
  width: 680rpx;
  height: auto;
}
.order-pages .popup-rule .rule-text {
  min-height: 300rpx;
  max-height: 60vh;
}
.order-pages .popup-rule .iconfont {
  font-size: 60rpx;
}
